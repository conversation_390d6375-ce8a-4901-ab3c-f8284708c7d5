name: jq
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  # Python版本
  - python=3.10
  # CUDA相关
  - cudatoolkit=11.8
  # 核心深度学习框架
  - pytorch>=2.1.0
  - torchvision>=0.16.0
  - torchaudio>=2.1.0
  # 数据处理与分析
  - pandas>=2.1.0
  - numpy>=1.24.0
  - scikit-learn>=1.3.0
  # 可视化
  - matplotlib>=3.7.0
  - seaborn>=0.12.0
  # 实用工具
  - pyyaml>=6.0.1
  - tqdm>=4.66.0
  - pip>=23.0
  # 通过pip安装的依赖
  - pip:
    # Transformers生态
    - transformers>=4.37.0
    - tokenizers>=0.15.0
    - datasets>=2.16.0
    - accelerate>=0.25.0
    # ModelScope支持
    - modelscope>=1.9.5
    # LoRA微调相关
    - peft>=0.8.0
    - bitsandbytes>=0.42.0
    # 模型优化
    - optimum>=1.16.0
    # 监控和可视化
    - tensorboard>=2.15.0
    - wandb>=0.16.0
    - rich>=13.7.0
    # 实用工具
    - fire>=0.5.0
    - jsonlines>=4.0.0
    # 模型评估
    - rouge-score>=0.1.2
    - nltk>=3.8.1
    # GPU监控
    - gpustat>=1.1.1
    - nvidia-ml-py>=12.535.133
    - GPUtil>=1.4.0
    - flash-attn>=2.3.0
