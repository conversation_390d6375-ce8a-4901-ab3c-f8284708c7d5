#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试内存修复效果的脚本
"""

import os
import torch
import gc
from config import Config
from model_trainer import clear_gpu_memory, print_gpu_memory_usage

def test_config_changes():
    """测试配置修改"""
    print("="*50)
    print("测试配置修改")
    print("="*50)
    
    config = Config()
    
    print("当前配置:")
    print(f"  训练批次大小: {config.training.per_device_train_batch_size}")
    print(f"  评估批次大小: {config.training.per_device_eval_batch_size}")
    print(f"  梯度累积步数: {config.training.gradient_accumulation_steps}")
    print(f"  有效批次大小: {config.training.per_device_train_batch_size * config.training.gradient_accumulation_steps}")
    print(f"  数据加载器工作进程: {config.training.dataloader_num_workers}")
    print(f"  预处理工作进程: {config.data.preprocessing_num_workers}")
    print(f"  最大序列长度: {config.data.max_seq_length}")
    print(f"  LoRA秩: {config.lora.r}")
    print(f"  LoRA alpha: {config.lora.lora_alpha}")

def test_memory_functions():
    """测试内存管理函数"""
    print("\n" + "="*50)
    print("测试内存管理函数")
    print("="*50)
    
    # 测试GPU内存清理
    print("清理GPU内存...")
    clear_gpu_memory()
    
    # 测试内存使用打印
    print("GPU内存使用情况:")
    print_gpu_memory_usage()

def test_environment_variables():
    """测试环境变量设置"""
    print("\n" + "="*50)
    print("测试环境变量设置")
    print("="*50)
    
    # 设置环境变量
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    print("环境变量设置:")
    print(f"  PYTORCH_CUDA_ALLOC_CONF: {os.environ.get('PYTORCH_CUDA_ALLOC_CONF', 'Not set')}")
    print(f"  TOKENIZERS_PARALLELISM: {os.environ.get('TOKENIZERS_PARALLELISM', 'Not set')}")
    print(f"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")

def test_torch_settings():
    """测试PyTorch设置"""
    print("\n" + "="*50)
    print("测试PyTorch设置")
    print("="*50)
    
    if torch.cuda.is_available():
        # 设置优化选项
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        
        print("PyTorch CUDA设置:")
        print(f"  TF32 (matmul): {torch.backends.cuda.matmul.allow_tf32}")
        print(f"  TF32 (cudnn): {torch.backends.cudnn.allow_tf32}")
        print(f"  CuDNN benchmark: {torch.backends.cudnn.benchmark}")
        
        # GPU信息
        gpu_count = torch.cuda.device_count()
        print(f"  GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            print(f"  GPU {i}: {props.name}")
            print(f"    总内存: {props.total_memory / 1024**3:.1f} GB")
            print(f"    多处理器数量: {props.multi_processor_count}")
    else:
        print("CUDA不可用")

def simulate_memory_allocation():
    """模拟内存分配测试"""
    print("\n" + "="*50)
    print("模拟内存分配测试")
    print("="*50)
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过测试")
        return
    
    try:
        # 清理内存
        clear_gpu_memory()
        
        print("分配前:")
        print_gpu_memory_usage()
        
        # 分配一些内存
        print("\n分配小张量...")
        tensor1 = torch.randn(1000, 1000, device='cuda')
        print("分配1000x1000张量后:")
        print_gpu_memory_usage()
        
        # 删除张量
        del tensor1
        clear_gpu_memory()
        print("\n清理后:")
        print_gpu_memory_usage()
        
        print("内存分配测试通过!")
        
    except torch.cuda.OutOfMemoryError as e:
        print(f"内存分配失败: {e}")
    except Exception as e:
        print(f"测试出错: {e}")

def main():
    """主函数"""
    print("内存修复效果测试")
    print("="*50)
    
    # 测试各个组件
    test_config_changes()
    test_environment_variables()
    test_torch_settings()
    test_memory_functions()
    simulate_memory_allocation()
    
    print("\n" + "="*50)
    print("测试完成!")
    print("="*50)
    
    print("\n建议:")
    print("1. 如果所有测试通过，可以尝试运行 train_memory_optimized.py")
    print("2. 如果仍有问题，请先运行 clear_gpu.py 清理GPU进程")
    print("3. 监控训练过程中的内存使用情况")

if __name__ == "__main__":
    main()
