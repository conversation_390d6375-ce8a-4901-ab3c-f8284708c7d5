#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化的Qwen2.5文本审核模型训练脚本
专门针对GPU内存不足问题进行优化
"""

import os
import sys
import gc
import torch
import psutil
import logging
from pathlib import Path

# 设置环境变量（在导入其他模块之前）
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"  # 用于调试

# 本地模块导入
from config import Config
from model_trainer import QwenModerationTrainer, clear_gpu_memory, print_gpu_memory_usage
from utils import setup_logging, get_gpu_info, get_system_info

def check_system_resources():
    """检查系统资源"""
    print("\n" + "="*60)
    print("系统资源检查")
    print("="*60)
    
    # CPU和内存信息
    cpu_count = psutil.cpu_count()
    memory = psutil.virtual_memory()
    print(f"CPU核心数: {cpu_count}")
    print(f"系统内存: {memory.total / 1024**3:.1f} GB")
    print(f"可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # GPU信息
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            total_memory = props.total_memory / 1024**3
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            
            print(f"GPU {i} ({props.name}):")
            print(f"  总内存: {total_memory:.1f} GB")
            print(f"  已分配: {allocated:.2f} GB")
            print(f"  已保留: {reserved:.2f} GB")
            print(f"  可用: {total_memory - reserved:.2f} GB")
    else:
        print("未检测到CUDA GPU")
    
    print("="*60)

def kill_existing_processes():
    """杀死可能占用GPU的现有进程"""
    print("\n检查并清理GPU进程...")
    
    try:
        # 使用nvidia-smi查找GPU进程
        import subprocess
        result = subprocess.run(['nvidia-smi', '--query-compute-apps=pid,process_name,used_memory', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            current_pid = os.getpid()
            
            for line in lines:
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        pid = int(parts[0])
                        process_name = parts[1]
                        memory_mb = int(parts[2])
                        
                        # 不杀死当前进程
                        if pid != current_pid:
                            print(f"发现GPU进程: PID={pid}, 进程名={process_name}, 内存={memory_mb}MB")
                            try:
                                # 尝试优雅地终止进程
                                process = psutil.Process(pid)
                                process.terminate()
                                process.wait(timeout=5)
                                print(f"已终止进程 {pid}")
                            except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                                try:
                                    # 强制杀死进程
                                    process.kill()
                                    print(f"已强制杀死进程 {pid}")
                                except psutil.NoSuchProcess:
                                    print(f"进程 {pid} 已不存在")
                            except Exception as e:
                                print(f"无法终止进程 {pid}: {e}")
        else:
            print("未发现GPU进程或nvidia-smi命令失败")
            
    except Exception as e:
        print(f"检查GPU进程时出错: {e}")

def optimize_memory_settings():
    """优化内存设置"""
    print("\n优化内存设置...")
    
    # 清理Python垃圾回收
    gc.collect()
    
    # 清理GPU缓存
    clear_gpu_memory()
    
    # 设置PyTorch内存分配策略
    if torch.cuda.is_available():
        # 重置内存统计
        torch.cuda.reset_peak_memory_stats()
        torch.cuda.reset_accumulated_memory_stats()
        
        # 设置内存分配策略
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        
        print("GPU内存优化设置完成")
    
    print("内存优化完成")

def create_optimized_config():
    """创建内存优化的配置"""
    config = Config()
    
    # 进一步减小批次大小和序列长度
    config.training.per_device_train_batch_size = 1
    config.training.per_device_eval_batch_size = 1
    config.training.gradient_accumulation_steps = 32  # 增大梯度累积以保持有效批次大小
    config.training.dataloader_num_workers = 0
    
    # 减小序列长度
    config.data.max_seq_length = 256  # 从512减小到256
    config.data.preprocessing_num_workers = 1
    
    # 使用更小的LoRA配置
    config.lora.r = 32  # 从64减小到32
    config.lora.lora_alpha = 64  # 相应调整
    
    # 更频繁的保存和评估（减小间隔）
    config.training.save_steps = 100
    config.training.eval_steps = 100
    config.training.logging_steps = 5
    
    # 启用混合精度训练
    config.training.bf16 = True
    config.training.tf32 = True
    
    return config

def main():
    """主函数"""
    print("启动内存优化的Qwen2.5文本审核模型训练")
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 1. 检查系统资源
        check_system_resources()
        
        # 2. 清理现有进程
        kill_existing_processes()
        
        # 3. 优化内存设置
        optimize_memory_settings()
        
        # 4. 创建优化配置
        config = create_optimized_config()
        
        # 5. 打印优化后的配置
        print("\n" + "="*60)
        print("内存优化配置")
        print("="*60)
        print(f"训练批次大小: {config.training.per_device_train_batch_size}")
        print(f"梯度累积步数: {config.training.gradient_accumulation_steps}")
        print(f"有效批次大小: {config.training.per_device_train_batch_size * config.training.gradient_accumulation_steps}")
        print(f"最大序列长度: {config.data.max_seq_length}")
        print(f"LoRA秩: {config.lora.r}")
        print(f"数据加载器工作进程: {config.training.dataloader_num_workers}")
        print(f"预处理工作进程: {config.data.preprocessing_num_workers}")
        print("="*60)
        
        # 6. 再次检查GPU内存
        print_gpu_memory_usage()
        
        # 7. 创建训练器
        logger.info("创建训练器...")
        trainer = QwenModerationTrainer(config)
        
        # 8. 开始训练
        logger.info("开始训练流程...")
        trainer.run_full_training()
        
        # 9. 训练完成
        logger.info("训练流程完成！")
        print(f"\n训练完成！")
        print(f"模型保存位置: {config.training.output_dir}")
        
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        print("\n训练被中断")
    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"GPU内存不足: {e}")
        print(f"\nGPU内存不足错误: {e}")
        print("\n建议:")
        print("1. 进一步减小批次大小")
        print("2. 减小序列长度")
        print("3. 减小LoRA秩")
        print("4. 检查是否有其他进程占用GPU")
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        print(f"\n训练失败: {e}")
        raise
    finally:
        # 清理GPU缓存
        clear_gpu_memory()
        gc.collect()

if __name__ == "__main__":
    main()
