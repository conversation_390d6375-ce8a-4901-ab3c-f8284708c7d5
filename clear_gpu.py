#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU进程清理脚本
用于清理占用GPU内存的进程
"""

import os
import sys
import subprocess
import psutil
import torch
import gc

def get_gpu_processes():
    """获取占用GPU的进程列表"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-compute-apps=pid,process_name,used_memory', 
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        processes = []
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        pid = int(parts[0])
                        process_name = parts[1]
                        memory_mb = int(parts[2])
                        processes.append({
                            'pid': pid,
                            'name': process_name,
                            'memory_mb': memory_mb
                        })
        return processes
    except Exception as e:
        print(f"获取GPU进程失败: {e}")
        return []

def kill_process(pid, force=False):
    """杀死指定进程"""
    try:
        process = psutil.Process(pid)
        process_name = process.name()
        
        if force:
            process.kill()
            print(f"强制杀死进程 {pid} ({process_name})")
        else:
            process.terminate()
            try:
                process.wait(timeout=5)
                print(f"优雅终止进程 {pid} ({process_name})")
            except psutil.TimeoutExpired:
                process.kill()
                print(f"超时后强制杀死进程 {pid} ({process_name})")
        
        return True
    except psutil.NoSuchProcess:
        print(f"进程 {pid} 不存在")
        return True
    except psutil.AccessDenied:
        print(f"没有权限杀死进程 {pid}")
        return False
    except Exception as e:
        print(f"杀死进程 {pid} 失败: {e}")
        return False

def clear_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        print("清理GPU缓存...")
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        
        # 重置内存统计
        torch.cuda.reset_peak_memory_stats()
        torch.cuda.reset_accumulated_memory_stats()
        
        print("GPU缓存清理完成")
    else:
        print("未检测到CUDA GPU")

def print_gpu_status():
    """打印GPU状态"""
    if torch.cuda.is_available():
        print("\nGPU状态:")
        gpu_count = torch.cuda.device_count()
        
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            total_memory = props.total_memory / 1024**3
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            
            print(f"GPU {i} ({props.name}):")
            print(f"  总内存: {total_memory:.1f} GB")
            print(f"  已分配: {allocated:.2f} GB")
            print(f"  已保留: {reserved:.2f} GB")
            print(f"  可用: {total_memory - reserved:.2f} GB")
    else:
        print("未检测到CUDA GPU")

def main():
    """主函数"""
    print("GPU进程清理工具")
    print("="*50)
    
    # 获取当前进程PID
    current_pid = os.getpid()
    
    # 获取GPU进程
    print("检查GPU进程...")
    gpu_processes = get_gpu_processes()
    
    if not gpu_processes:
        print("未发现GPU进程")
    else:
        print(f"发现 {len(gpu_processes)} 个GPU进程:")
        for proc in gpu_processes:
            status = "(当前进程)" if proc['pid'] == current_pid else ""
            print(f"  PID: {proc['pid']}, 进程: {proc['name']}, 内存: {proc['memory_mb']} MB {status}")
        
        # 询问是否清理
        if len(gpu_processes) > 0:
            print("\n选项:")
            print("1. 清理所有GPU进程（除当前进程）")
            print("2. 选择性清理进程")
            print("3. 仅清理GPU缓存")
            print("4. 退出")
            
            try:
                choice = input("\n请选择操作 (1-4): ").strip()
                
                if choice == "1":
                    # 清理所有进程
                    print("\n清理所有GPU进程...")
                    for proc in gpu_processes:
                        if proc['pid'] != current_pid:
                            kill_process(proc['pid'])
                
                elif choice == "2":
                    # 选择性清理
                    print("\n选择要清理的进程 (输入PID，多个PID用空格分隔):")
                    pids_input = input("PIDs: ").strip()
                    if pids_input:
                        try:
                            pids = [int(pid) for pid in pids_input.split()]
                            for pid in pids:
                                if pid != current_pid:
                                    kill_process(pid)
                                else:
                                    print(f"跳过当前进程 {pid}")
                        except ValueError:
                            print("无效的PID格式")
                
                elif choice == "3":
                    # 仅清理GPU缓存
                    print("\n仅清理GPU缓存...")
                
                elif choice == "4":
                    print("退出")
                    return
                
                else:
                    print("无效选择")
                    return
                    
            except KeyboardInterrupt:
                print("\n操作被取消")
                return
    
    # 清理Python垃圾回收
    print("\n清理Python垃圾回收...")
    gc.collect()
    
    # 清理GPU内存
    clear_gpu_memory()
    
    # 等待一下让系统更新
    import time
    time.sleep(2)
    
    # 再次检查GPU进程
    print("\n清理后的GPU进程:")
    gpu_processes_after = get_gpu_processes()
    if not gpu_processes_after:
        print("无GPU进程")
    else:
        for proc in gpu_processes_after:
            status = "(当前进程)" if proc['pid'] == current_pid else ""
            print(f"  PID: {proc['pid']}, 进程: {proc['name']}, 内存: {proc['memory_mb']} MB {status}")
    
    # 打印GPU状态
    print_gpu_status()
    
    print("\n清理完成!")

if __name__ == "__main__":
    main()
