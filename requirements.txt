# Qwen2.5文本审核模型微调环境依赖
# 适用于Ubuntu + A800单卡环境

# 核心深度学习框架
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0

# Transformers生态系统
transformers>=4.37.0
tokenizers>=0.15.0
datasets>=2.16.0
accelerate>=0.25.0

# LoRA微调相关
peft>=0.8.0
bitsandbytes>=0.42.0

# 模型量化和优化
optimum>=1.16.0

# 数据处理和分析
pandas>=2.1.0
numpy>=1.24.0
scikit-learn>=1.3.0

# 可视化和监控
matplotlib>=3.7.0
seaborn>=0.12.0
tensorboard>=2.15.0
wandb>=0.16.0

# 实用工具
tqdm>=4.66.0
rich>=13.7.0
fire>=0.5.0

# JSON和配置文件处理
pyyaml>=6.0.1
jsonlines>=4.0.0

# 模型评估
rouge-score>=0.1.2
nltk>=3.8.1

# GPU监控
gpustat>=1.1.1
nvidia-ml-py>=12.535.133
GPUtil>=1.4.0
flash-attn>=2.3.0
