# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型微调配置文件
适用于单卡A800环境的LoRA微调
"""

import os
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
import torch

@dataclass
class ModelConfig:
    """模型相关配置"""
    # 基础模型配置
    model_name_or_path: str = "Qwen/Qwen2.5-7B-Instruct"  # Qwen2.5-7B模型路径
    model_revision: str = "main"  # 模型版本
    cache_dir: str = "./hf_cache"
    trust_remote_code: bool = True  # Qwen模型需要设置为True
    torch_dtype: str = "bfloat16"  # 可选: ["float16", "bfloat16", "float32"]
    device_map: str = "cuda:0"  # 指定单GPU，避免auto导致的内存问题
    attn_implementation: str = "eager"  # 使用默认注意力实现，避免安装flash-attn编译耗时
    
    # 模型加载配置
    use_cache: bool = False  # 训练时关闭缓存
    low_cpu_mem_usage: bool = True  # 降低CPU内存使用

@dataclass
class LoRAConfig:
    """LoRA微调配置"""
    # LoRA核心参数
    r: int = 64  # LoRA秩，控制参数量和性能平衡，可调范围[8, 16, 32, 64, 128]
    lora_alpha: int = 128  # LoRA缩放参数，通常设为r的2倍
    lora_dropout: float = 0.1  # LoRA dropout率，防止过拟合
    
    # 目标模块配置
    target_modules: List[str] = field(default_factory=lambda: [
        "q_proj",    # 查询投影层
        "k_proj",    # 键投影层  
        "v_proj",    # 值投影层
        "o_proj",    # 输出投影层
        "gate_proj", # 门控投影层
        "up_proj",   # 上投影层
        "down_proj", # 下投影层
    ])
    
    # 其他LoRA参数
    bias: str = "none"  # 偏置处理方式
    task_type: str = "CAUSAL_LM"  # 任务类型
    fan_in_fan_out: bool = False  # 扇入扇出
    init_lora_weights: bool = True  # 初始化LoRA权重

@dataclass
class TrainingConfig:
    """训练相关配置"""
    # 基础训练参数
    output_dir: str = "./output"  # 输出目录
    num_train_epochs: int = 3  # 训练轮数，可根据数据量调整[1-10]
    per_device_train_batch_size: int = 1  # 单卡训练批次大小，减小以节省内存
    per_device_eval_batch_size: int = 1  # 单卡评估批次大小，减小以节省内存
    gradient_accumulation_steps: int = 16  # 梯度累积步数，增大以保持有效批次大小=1*16=16
    
    # 学习率配置
    learning_rate: float = 2e-4  # 学习率，LoRA推荐范围[1e-4, 5e-4]
    weight_decay: float = 0.01  # 权重衰减
    lr_scheduler_type: str = "cosine"  # 学习率调度器类型
    warmup_ratio: float = 0.1  # 预热比例
    
    # 优化器配置
    optim: str = "adamw_torch"  # 优化器类型
    adam_beta1: float = 0.9  # Adam beta1参数
    adam_beta2: float = 0.999  # Adam beta2参数
    adam_epsilon: float = 1e-8  # Adam epsilon参数
    max_grad_norm: float = 1.0  # 梯度裁剪
    
    # 保存和日志配置
    save_strategy: str = "steps"  # 保存策略
    save_steps: int = 500  # 保存步数间隔
    save_total_limit: int = 3  # 最大保存检查点数量
    logging_strategy: str = "steps"  # 日志策略
    logging_steps: int = 10  # 日志步数间隔
    
    # 评估配置
    evaluation_strategy: str = "steps"  # 评估策略
    eval_steps: int = 500  # 评估步数间隔
    load_best_model_at_end: bool = True  # 训练结束时加载最佳模型
    metric_for_best_model: str = "eval_loss"  # 最佳模型评估指标
    greater_is_better: bool = False  # 指标越小越好
    
    # 内存和性能优化
    dataloader_num_workers: int = 0  # 数据加载器工作进程数，设为0以节省内存
    remove_unused_columns: bool = False  # 保留未使用的列
    group_by_length: bool = True  # 按长度分组，提升效率
    ddp_find_unused_parameters: bool = False  # DDP未使用参数查找
    
    # 混合精度训练
    fp16: bool = False  # 是否使用fp16
    bf16: bool = True  # 是否使用bf16，A800推荐
    tf32: bool = True  # 是否使用tf32，A800支持
    
    # 其他配置
    seed: int = 42  # 随机种子
    report_to: List[str] = field(default_factory=lambda: ["tensorboard"])  # 报告工具
    run_name: str = "qwen2.5-text-moderation"  # 运行名称

@dataclass
class DataConfig:
    """数据相关配置"""
    # 数据路径
    train_file: str = "./data/train.jsonl"  # 训练数据文件
    validation_file: str = "./data/validation.jsonl"  # 验证数据文件
    test_file: str = "./data/test.jsonl"  # 测试数据文件

    # 数据处理参数
    max_seq_length: int = 512  # 最大序列长度，根据审核文本长度调整
    preprocessing_num_workers: int = 1  # 预处理工作进程数，减少以节省内存
    overwrite_cache: bool = False  # 是否覆盖缓存
    
    # 数据格式配置
    text_column: str = "text"  # 文本列名
    label_column: str = "label"  # 标签列名
    
    # 审核标签配置
    label_mapping: Dict[str, int] = field(default_factory=lambda: {
        "合规": 0,      # 内容合规
        "违规": 1,      # 内容违规
        "疑似": 2,      # 疑似违规，需人工审核
    })
    
    # 数据增强配置
    use_data_augmentation: bool = False  # 是否使用数据增强
    augmentation_ratio: float = 0.1  # 数据增强比例

@dataclass
class SystemConfig:
    """系统相关配置"""
    # GPU配置
    cuda_visible_devices: str = "0"  # 可见GPU设备
    use_cuda: bool = True  # 是否使用CUDA
    
    # 内存配置
    max_memory_mb: int = 79000  # 最大内存使用量(MB)，A800为80GB
    cpu_count: int = os.cpu_count()  # CPU核心数
    
    # 缓存配置
    cache_dir: str = "./cache"  # 缓存目录
    hf_cache_dir: str = "./hf_cache"  # HuggingFace缓存目录

class Config:
    """主配置类，整合所有配置"""
    
    def __init__(self):
        self.model = ModelConfig()
        self.lora = LoRAConfig()
        self.training = TrainingConfig()
        self.data = DataConfig()
        self.system = SystemConfig()
    
    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                if isinstance(value, dict):
                    config_obj = getattr(self, key)
                    for sub_key, sub_value in value.items():
                        if hasattr(config_obj, sub_key):
                            setattr(config_obj, sub_key, sub_value)
                        else:
                            print(f"警告: {key}.{sub_key} 不是有效的配置参数")
                else:
                    setattr(self, key, value)
            else:
                print(f"警告: {key} 不是有效的配置参数")
    
    def save_config(self, path: str):
        """保存配置到文件"""
        import json
        config_dict = {
            "model": self.model.__dict__,
            "lora": self.lora.__dict__,
            "training": self.training.__dict__,
            "data": self.data.__dict__,
            "system": self.system.__dict__,
        }
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        print(f"配置已保存到: {path}")
    
    def load_config(self, path: str):
        """从文件加载配置"""
        import json
        with open(path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        for key, value in config_dict.items():
            if hasattr(self, key):
                config_obj = getattr(self, key)
                for sub_key, sub_value in value.items():
                    if hasattr(config_obj, sub_key):
                        setattr(config_obj, sub_key, sub_value)
        print(f"配置已从 {path} 加载")
    
    def print_config(self):
        """打印当前配置"""
        print("=" * 50)
        print("Qwen2.5文本审核模型微调配置")
        print("=" * 50)
        
        print("\n[模型配置]")
        for key, value in self.model.__dict__.items():
            print(f"  {key}: {value}")
        
        print("\n[LoRA配置]")
        for key, value in self.lora.__dict__.items():
            print(f"  {key}: {value}")
        
        print("\n[训练配置]")
        for key, value in self.training.__dict__.items():
            print(f"  {key}: {value}")
        
        print("\n[数据配置]")
        for key, value in self.data.__dict__.items():
            print(f"  {key}: {value}")
        
        print("\n[系统配置]")
        for key, value in self.system.__dict__.items():
            print(f"  {key}: {value}")
        
        print("=" * 50)

# 创建默认配置实例
default_config = Config()

if __name__ == "__main__":
    # 测试配置
    config = Config()
    config.print_config()
    
    # 保存配置示例
    config.save_config("./config.json")
