# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核数据处理模块
负责数据加载、预处理、格式化等功能
"""

import json
import jsonlines
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datasets import Dataset, DatasetDict, load_dataset
from transformers import PreTrainedTokenizer
import torch
from torch.utils.data import DataLoader
import logging
from pathlib import Path
import random
from sklearn.model_selection import train_test_split
from collections import Counter

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TextModerationDataProcessor:
    """文本审核数据处理器"""
    
    def __init__(self, tokenizer: PreTrainedTokenizer, config):
        """
        初始化数据处理器
        
        Args:
            tokenizer: 分词器
            config: 配置对象
        """
        self.tokenizer = tokenizer
        self.config = config
        self.label_mapping = config.data.label_mapping
        self.reverse_label_mapping = {v: k for k, v in self.label_mapping.items()}
        
        # 设置特殊token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_jsonl_data(self, file_path: str) -> List[Dict]:
        """
        加载JSONL格式数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            数据列表
        """
        data = []
        try:
            with jsonlines.open(file_path, 'r') as reader:
                for item in reader:
                    data.append(item)
            logger.info(f"成功加载 {len(data)} 条数据从 {file_path}")
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
        
        return data
    
    def create_sample_data(self, output_dir: str = "./data", num_samples: int = 1000):
        """
        创建示例审核数据
        
        Args:
            output_dir: 输出目录
            num_samples: 样本数量
        """
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 示例审核文本数据
        sample_texts = {
            "合规": [
                "今天天气真好，适合出门散步。",
                "这本书的内容很有趣，推荐大家阅读。",
                "公司新推出的产品功能很实用。",
                "学习新技能需要持续的努力和练习。",
                "健康的生活方式包括合理饮食和适量运动。",
                "科技的发展为我们的生活带来了便利。",
                "团队合作是项目成功的关键因素。",
                "保护环境是每个人的责任。",
                "教育对个人发展非常重要。",
                "艺术能够丰富我们的精神世界。"
            ],
            "违规": [
                "这个产品质量太差了，完全是垃圾！",
                "某某公司就是骗子，大家千万别相信！",
                "我要报复那些伤害过我的人。",
                "传播一些不实的谣言信息。",
                "发布涉及政治敏感的不当言论。",
                "恶意攻击他人的人格和尊严。",
                "散布可能引起恐慌的虚假消息。",
                "发表歧视性的言论和观点。",
                "涉及违法犯罪的相关内容。",
                "传播有害的极端思想。"
            ],
            "疑似": [
                "这个决定可能会引起一些争议。",
                "某些做法确实存在问题。",
                "这种说法有待进一步验证。",
                "情况比较复杂，需要仔细分析。",
                "不同的人可能会有不同的看法。",
                "这个话题比较敏感，需要谨慎对待。",
                "相关信息还需要进一步确认。",
                "存在一定的风险和不确定性。",
                "可能涉及一些争议性的内容。",
                "需要更多证据来支持这个观点。"
            ]
        }
        
        # 生成训练、验证、测试数据
        datasets = {}
        for split, ratio in [("train", 0.7), ("validation", 0.2), ("test", 0.1)]:
            split_data = []
            split_samples = int(num_samples * ratio)
            
            for label, texts in sample_texts.items():
                label_samples = split_samples // len(sample_texts)
                for i in range(label_samples):
                    text = random.choice(texts)
                    # 添加一些随机变化
                    if random.random() < 0.3:
                        text = text + f" (变体{i})"
                    
                    split_data.append({
                        "text": text,
                        "label": label,
                        "label_id": self.label_mapping[label]
                    })
            
            # 随机打乱数据
            random.shuffle(split_data)
            
            # 保存数据
            output_file = Path(output_dir) / f"{split}.jsonl"
            with jsonlines.open(output_file, 'w') as writer:
                for item in split_data:
                    writer.write(item)
            
            datasets[split] = split_data
            logger.info(f"生成 {split} 数据: {len(split_data)} 条样本")
        
        # 打印数据统计
        self.print_data_statistics(datasets)
        logger.info(f"示例数据已保存到 {output_dir}")
    
    def print_data_statistics(self, datasets: Dict[str, List[Dict]]):
        """
        打印数据统计信息
        
        Args:
            datasets: 数据集字典
        """
        print("\n" + "="*50)
        print("数据集统计信息")
        print("="*50)
        
        for split_name, data in datasets.items():
            print(f"\n[{split_name.upper()}]")
            print(f"总样本数: {len(data)}")
            
            # 标签分布
            label_counts = Counter([item['label'] for item in data])
            print("标签分布:")
            for label, count in label_counts.items():
                percentage = count / len(data) * 100
                print(f"  {label}: {count} ({percentage:.1f}%)")
            
            # 文本长度统计
            text_lengths = [len(item['text']) for item in data]
            print(f"文本长度统计:")
            print(f"  平均长度: {np.mean(text_lengths):.1f}")
            print(f"  最小长度: {min(text_lengths)}")
            print(f"  最大长度: {max(text_lengths)}")
            print(f"  中位数长度: {np.median(text_lengths):.1f}")
    
    def format_instruction(self, text: str, label: Optional[str] = None) -> str:
        """
        格式化指令数据
        
        Args:
            text: 输入文本
            label: 标签（训练时提供，推理时为None）
            
        Returns:
            格式化后的指令文本
        """
        # 构建系统提示
        system_prompt = """你是一个专业的文本内容审核助手。请对给定的文本内容进行审核，判断其是否符合平台规范。

审核标准：
- 合规：内容健康正面，符合平台规范
- 违规：内容明显违反平台规范，包含有害信息
- 疑似：内容存在争议或需要进一步人工审核

请仅回答：合规、违规、疑似 中的一个。"""
        
        # 构建用户输入
        user_input = f"请审核以下文本内容：\n{text}"
        
        if label is not None:
            # 训练时的完整对话格式
            conversation = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n{label}<|im_end|>"
        else:
            # 推理时的输入格式
            conversation = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
        
        return conversation
    
    def tokenize_function(self, examples: Dict[str, List]) -> Dict[str, List]:
        """
        分词函数
        
        Args:
            examples: 批量样本
            
        Returns:
            分词后的结果
        """
        # 格式化指令
        formatted_texts = []
        for i in range(len(examples[self.config.data.text_column])):
            text = examples[self.config.data.text_column][i]
            label = examples[self.config.data.label_column][i] if self.config.data.label_column in examples else None
            formatted_text = self.format_instruction(text, label)
            formatted_texts.append(formatted_text)
        
        # 分词 - 使用固定长度padding
        tokenized = self.tokenizer(
            formatted_texts,
            truncation=True,
            padding="max_length",  # 使用固定长度padding，确保所有序列长度一致
            max_length=self.config.data.max_seq_length,
            return_tensors=None,
        )
        
        # 设置labels（用于计算loss）
        tokenized["labels"] = tokenized["input_ids"].copy()
        
        return tokenized
    
    def prepare_datasets(self) -> DatasetDict:
        """
        准备训练数据集
        
        Returns:
            处理后的数据集
        """
        datasets = {}
        
        # 检查数据文件是否存在，不存在则创建示例数据
        data_dir = Path(self.config.data.train_file).parent
        if not Path(self.config.data.train_file).exists():
            logger.warning("训练数据文件不存在，创建示例数据...")
            self.create_sample_data(str(data_dir))
        
        # 加载各个数据集
        for split, file_path in [
            ("train", self.config.data.train_file),
            ("validation", self.config.data.validation_file),
            ("test", self.config.data.test_file)
        ]:
            if Path(file_path).exists():
                # 加载JSONL数据
                data_list = self.load_jsonl_data(file_path)
                
                # 转换为Dataset格式
                dataset = Dataset.from_list(data_list)
                
                # 应用分词
                tokenized_dataset = dataset.map(
                    self.tokenize_function,
                    batched=True,
                    num_proc=self.config.data.preprocessing_num_workers,
                    remove_columns=dataset.column_names,
                    desc=f"分词 {split} 数据集",
                )
                
                datasets[split] = tokenized_dataset
                logger.info(f"处理完成 {split} 数据集: {len(tokenized_dataset)} 条样本")
            else:
                logger.warning(f"数据文件不存在: {file_path}")
        
        return DatasetDict(datasets)
    
    def data_collator(self, features: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """
        数据整理器，用于批量处理
        
        Args:
            features: 批量特征
            
        Returns:
            整理后的批量数据
        """
        # 获取批量中的最大长度
        max_length = max(len(f["input_ids"]) for f in features)
        
        # 准备批量数据
        batch = {}
        
        for key in ["input_ids", "attention_mask", "labels"]:
            batch[key] = []
            
            for feature in features:
                value = feature[key]
                
                # 填充到最大长度
                if key in ["input_ids", "labels"]:
                    # 对于input_ids和labels，使用pad_token_id填充
                    pad_value = self.tokenizer.pad_token_id if key == "input_ids" else -100
                    padded_value = value + [pad_value] * (max_length - len(value))
                else:
                    # 对于attention_mask，使用0填充
                    padded_value = value + [0] * (max_length - len(value))
                
                batch[key].append(padded_value)
        
        # 转换为tensor
        for key in batch:
            batch[key] = torch.tensor(batch[key], dtype=torch.long)
        
        return batch
    
    def create_data_loaders(self, datasets: DatasetDict) -> Dict[str, DataLoader]:
        """
        创建数据加载器
        
        Args:
            datasets: 数据集
            
        Returns:
            数据加载器字典
        """
        data_loaders = {}
        
        for split_name, dataset in datasets.items():
            shuffle = split_name == "train"  # 只有训练集需要打乱
            
            batch_size = (
                self.config.training.per_device_train_batch_size 
                if split_name == "train" 
                else self.config.training.per_device_eval_batch_size
            )
            
            data_loader = DataLoader(
                dataset,
                batch_size=batch_size,
                shuffle=shuffle,
                collate_fn=self.data_collator,
                num_workers=self.config.training.dataloader_num_workers,
                pin_memory=True,
            )
            
            data_loaders[split_name] = data_loader
            logger.info(f"创建 {split_name} 数据加载器: {len(data_loader)} 个批次")
        
        return data_loaders
    
    def preview_data(self, datasets: DatasetDict, num_samples: int = 3):
        """
        预览数据集样本
        
        Args:
            datasets: 数据集
            num_samples: 预览样本数量
        """
        print("\n" + "="*50)
        print("数据集样本预览")
        print("="*50)
        
        for split_name, dataset in datasets.items():
            print(f"\n[{split_name.upper()} 数据集]")
            
            for i in range(min(num_samples, len(dataset))):
                sample = dataset[i]
                
                # 解码input_ids
                decoded_text = self.tokenizer.decode(sample["input_ids"], skip_special_tokens=False)
                
                print(f"\n样本 {i+1}:")
                print(f"Input IDs长度: {len(sample['input_ids'])}")
                print(f"解码文本:")
                print(decoded_text)
                print("-" * 30)

def main():
    """测试数据处理器"""
    from transformers import AutoTokenizer
    from config import Config
    
    # 加载配置和分词器
    config = Config()
    tokenizer = AutoTokenizer.from_pretrained(
        config.model.model_name_or_path,
        trust_remote_code=True
    )
    
    # 创建数据处理器
    processor = TextModerationDataProcessor(tokenizer, config)
    
    # 创建示例数据
    processor.create_sample_data()
    
    # 准备数据集
    datasets = processor.prepare_datasets()
    
    # 预览数据
    processor.preview_data(datasets)
    
    # 创建数据加载器
    data_loaders = processor.create_data_loaders(datasets)
    
    print(f"\n数据处理完成！")
    print(f"训练集: {len(datasets['train'])} 条样本")
    print(f"验证集: {len(datasets['validation'])} 条样本")
    print(f"测试集: {len(datasets['test'])} 条样本")

if __name__ == "__main__":
    main()
