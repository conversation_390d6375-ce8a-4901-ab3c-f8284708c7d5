#!/bin/bash
# -*- coding: utf-8 -*-
# Qwen2.5文本审核模型微调初始化脚本
# 适用于conda环境已创建的情况

echo "=========================================="
echo "Qwen2.5文本审核模型初始化"
echo "=========================================="

# 创建项目目录结构
echo "创建项目目录结构..."
mkdir -p data
mkdir -p output
mkdir -p cache
mkdir -p hf_cache
mkdir -p logs
mkdir -p plots

# 设置环境变量
echo "设置环境变量..."
export CUDA_VISIBLE_DEVICES=0
export TOKENIZERS_PARALLELISM=false
export HF_HOME=./hf_cache
# 注意: TRANSFORMERS_CACHE 已弃用，只使用 HF_HOME

# 设置ModelScope镜像加速模型下载
echo "配置ModelScope镜像..."
export HF_ENDPOINT="https://modelscope.cn/api/v1/models"
export MODELSCOPE_MIRROR=1

# 提示模型下载
echo ""
echo "说明: 您可以选择从下面四种途径之一下载模型"
echo "1. 直接从原始 Hugging Face 下载"
echo "2. 使用 ModelScope SDK 下载模型"
echo "3. 自定义模型路径下载"
echo "4. 跳过模型下载"
read -p "请选择选项 (1/2/3/4): " download_option

if [ "$download_option" = "1" ]; then
    echo "下载原始 Hugging Face模型..."
    # 移除可能影响下载的环境变量
    unset HF_ENDPOINT
    unset MODELSCOPE_MIRROR
    
    python -c "
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

print('开始下载模型...')
model_name = 'Qwen/Qwen2.5-7B-Instruct'

# 下载分词器
print('下载分词器...')
tokenizer = AutoTokenizer.from_pretrained(
    model_name,
    trust_remote_code=True,
    cache_dir='./hf_cache'
)

# 下载模型
print('下载模型...')
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.bfloat16,
    trust_remote_code=True,
    cache_dir='./hf_cache'
)

print('模型下载完成!')
"

elif [ "$download_option" = "2" ]; then
    echo "使用 ModelScope SDK 下载 Qwen2.5-7B-Instruct 模型..."
    
    python -c "
import os
from modelscope import snapshot_download
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

print('开始使用 ModelScope SDK 下载模型...')

# 使用modelscope下载模型
print('下载模型到本地目录...')
model_dir = snapshot_download('qwen/Qwen2.5-7B-Instruct', cache_dir='./hf_cache')
print(f'模型下载完成，保存在目录: {model_dir}')

# 加载分词器和模型以验证
print('验证模型和分词器...')
tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(
    model_dir,
    torch_dtype=torch.bfloat16,  # 采用 bfloat16 精度优化 A800 GPU 运行性能
    trust_remote_code=True,
    device_map='auto'
)

print('模型加载与验证完成!')
print(f'请在配置文件中将模型路径设置为: {model_dir}')
"

elif [ "$download_option" = "3" ]; then
    read -p "输入模型路径 (例如: QWen/Qwen2-7B-beta): " custom_model_path
    echo "下载自定义模型: $custom_model_path"
    
    python -c "
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

print('开始下载模型...')
model_name = '$custom_model_path'

# 下载分词器
print('下载分词器...')
tokenizer = AutoTokenizer.from_pretrained(
    model_name,
    trust_remote_code=True,
    cache_dir='./hf_cache'
)

# 下载模型
print('下载模型...')
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.bfloat16,
    trust_remote_code=True,
    cache_dir='./hf_cache'
)

print('模型下载完成!')
"

elif [ "$download_option" = "4" ]; then
    echo "跳过模型下载..."
else
    echo "无效选择，跳过模型下载..."
fi

# 创建示例数据
echo ""
read -p "是否创建示例训练数据? (y/n): " create_data
if [ "$create_data" = "y" ] || [ "$create_data" = "Y" ]; then
    echo "创建示例数据..."
    python -c "
import os
import sys
from transformers import AutoTokenizer
from data_processor import TextModerationDataProcessor
from config import Config

def get_tokenizer():
    try:
        # 方式1: 使用ModelScope SDK获取模型(适用于中国境内环境)
        print('尝试使用ModelScope SDK加载分词器...')
        try:
            from modelscope import snapshot_download
            model_dir = snapshot_download('qwen/Qwen2.5-7B-Instruct', cache_dir='./hf_cache')
            tokenizer = AutoTokenizer.from_pretrained(model_dir, trust_remote_code=True)
            print(f'成功使用ModelScope加载分词器: {model_dir}')
            return tokenizer
        except Exception as e:
            print(f'使用ModelScope加载失败: {e}')
            
        # 方式2: 使用Hugging Face路径
        print('尝试使用Hugging Face路径加载分词器...')
        # 临时取消可能影响下载的环境变量
        if 'HF_ENDPOINT' in os.environ:
            del os.environ['HF_ENDPOINT']
        if 'MODELSCOPE_MIRROR' in os.environ:
            del os.environ['MODELSCOPE_MIRROR']
        
        # 使用Qwen2.5-7B-Instruct，与前面下载的模型保持一致
        tokenizer = AutoTokenizer.from_pretrained('Qwen/Qwen2.5-7B-Instruct', trust_remote_code=True)
        print('成功使用Qwen2.5-7B-Instruct分词器')
        return tokenizer
    except Exception as e:
        print(f'分词器加载失败: {e}')
        print('为了创建示例数据，将使用简化版本的分词器')
        return None

# 创建示例数据不一定需要真实的分词器
# 因为TextModerationDataProcessor类在生成示例数据时不依赖分词器的具体功能

config = Config()
tokenizer = get_tokenizer()

processor = TextModerationDataProcessor(tokenizer, config)
processor.create_sample_data('./data', 1000)
print('示例数据创建完成!')
"
fi

# 测试配置
echo "测试配置..."
python -c "
from config import Config
config = Config()
config.print_config()
"

echo ""
echo "=========================================="
echo "初始化完成!"
echo "=========================================="
echo ""
echo "下一步操作:"
echo "1. 准备训练数据 (如果未创建示例数据)"
echo "2. 开始训练: python train.py"
echo "3. 查看帮助: python train.py --help"
echo ""
echo "常用命令:"
echo "- 创建示例数据: python train.py --create_sample_data"
echo "- 开始训练: python train.py"
echo "- 模型推理: python inference.py --help"
echo ""
echo "注意事项:"
echo "- 使用前请确保已激活conda环境: conda activate jq"
echo "- 环境变量仅在当前终端会话有效"
echo "- 确保有足够的磁盘空间 (建议100GB+)"
echo "- 训练过程中监控GPU内存使用"
echo "- 可以通过修改config.py调整参数"
