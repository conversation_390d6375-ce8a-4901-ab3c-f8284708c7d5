# -*- coding: utf-8 -*-
"""
最小内存使用的配置文件
专门针对内存不足问题优化
"""

import os
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
import torch

@dataclass
class ModelConfig:
    """模型相关配置 - 最小内存版本"""
    # 基础模型配置
    model_name_or_path: str = "Qwen/Qwen2.5-7B-Instruct"
    model_revision: str = "main"
    cache_dir: str = "./hf_cache"
    trust_remote_code: bool = True
    torch_dtype: str = "bfloat16"
    device_map: str = "cuda:0"  # 明确指定单GPU
    attn_implementation: str = "eager"
    
    # 模型加载配置 - 最大化内存节省
    use_cache: bool = False
    low_cpu_mem_usage: bool = True

@dataclass
class LoRAConfig:
    """LoRA微调配置 - 最小参数版本"""
    # 使用最小的LoRA参数以节省内存
    r: int = 16  # 进一步减小
    lora_alpha: int = 32  # 相应调整
    lora_dropout: float = 0.1
    
    # 减少目标模块以节省内存
    target_modules: List[str] = field(default_factory=lambda: [
        "q_proj",    # 只保留最关键的模块
        "v_proj",
        "o_proj",
    ])
    
    bias: str = "none"
    task_type: str = "CAUSAL_LM"
    fan_in_fan_out: bool = False
    init_lora_weights: bool = True

@dataclass
class TrainingConfig:
    """训练相关配置 - 最小内存版本"""
    # 基础训练参数
    output_dir: str = "./output"
    num_train_epochs: int = 1  # 减少训练轮数用于测试
    per_device_train_batch_size: int = 1
    per_device_eval_batch_size: int = 1
    gradient_accumulation_steps: int = 64  # 大幅增加以保持有效批次大小
    
    # 学习率配置
    learning_rate: float = 1e-4  # 稍微降低学习率
    weight_decay: float = 0.01
    lr_scheduler_type: str = "cosine"
    warmup_ratio: float = 0.05  # 减少预热
    
    # 优化器配置
    optim: str = "adamw_torch"
    adam_beta1: float = 0.9
    adam_beta2: float = 0.999
    adam_epsilon: float = 1e-8
    max_grad_norm: float = 1.0
    
    # 保存和日志配置
    save_strategy: str = "steps"
    save_steps: int = 50  # 更频繁保存
    save_total_limit: int = 2  # 减少保存数量
    logging_strategy: str = "steps"
    logging_steps: int = 5
    
    # 评估配置
    evaluation_strategy: str = "steps"
    eval_steps: int = 50
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_loss"
    greater_is_better: bool = False
    
    # 内存和性能优化 - 最激进设置
    dataloader_num_workers: int = 0  # 完全禁用多进程
    remove_unused_columns: bool = False
    group_by_length: bool = False  # 禁用以节省内存
    ddp_find_unused_parameters: bool = False
    
    # 混合精度训练
    fp16: bool = False
    bf16: bool = True
    tf32: bool = True
    
    # 其他配置
    seed: int = 42
    report_to: List[str] = field(default_factory=lambda: [])  # 禁用报告
    run_name: str = "qwen2.5-minimal"

@dataclass
class DataConfig:
    """数据相关配置 - 最小内存版本"""
    # 数据路径
    train_file: str = "./data/train.jsonl"
    validation_file: str = "./data/validation.jsonl"
    test_file: str = "./data/test.jsonl"
    
    # 数据处理参数 - 最小设置
    max_seq_length: int = 128  # 大幅减小序列长度
    preprocessing_num_workers: int = 1
    overwrite_cache: bool = False
    
    # 数据格式配置
    text_column: str = "text"
    label_column: str = "label"
    
    # 审核标签配置
    label_mapping: Dict[str, int] = field(default_factory=lambda: {
        "合规": 0,
        "违规": 1,
        "疑似": 2,
    })
    
    # 禁用数据增强
    use_data_augmentation: bool = False
    augmentation_ratio: float = 0.0

@dataclass
class SystemConfig:
    """系统相关配置 - 最小内存版本"""
    # GPU配置
    cuda_visible_devices: str = "0"
    use_cuda: bool = True
    
    # 内存配置
    max_memory_mb: int = 15000  # 限制为15GB
    cpu_count: int = 1  # 限制CPU使用
    
    # 缓存配置
    cache_dir: str = "./cache"
    hf_cache_dir: str = "./hf_cache"

class MinimalConfig:
    """最小内存配置类"""
    
    def __init__(self):
        self.model = ModelConfig()
        self.lora = LoRAConfig()
        self.training = TrainingConfig()
        self.data = DataConfig()
        self.system = SystemConfig()
    
    def print_config(self):
        """打印当前配置"""
        print("=" * 50)
        print("最小内存配置")
        print("=" * 50)
        
        print("\n[关键内存优化设置]")
        print(f"  序列长度: {self.data.max_seq_length}")
        print(f"  批次大小: {self.training.per_device_train_batch_size}")
        print(f"  梯度累积: {self.training.gradient_accumulation_steps}")
        print(f"  LoRA秩: {self.lora.r}")
        print(f"  目标模块数: {len(self.lora.target_modules)}")
        print(f"  工作进程: {self.training.dataloader_num_workers}")
        print(f"  设备映射: {self.model.device_map}")
        
        print("\n[模型配置]")
        for key, value in self.model.__dict__.items():
            print(f"  {key}: {value}")
        
        print("\n[LoRA配置]")
        for key, value in self.lora.__dict__.items():
            print(f"  {key}: {value}")
        
        print("\n[训练配置]")
        for key, value in self.training.__dict__.items():
            print(f"  {key}: {value}")
        
        print("=" * 50)
    
    def save_config(self, path: str):
        """保存配置到文件"""
        import json
        config_dict = {
            "model": self.model.__dict__,
            "lora": self.lora.__dict__,
            "training": self.training.__dict__,
            "data": self.data.__dict__,
            "system": self.system.__dict__,
        }
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        print(f"最小配置已保存到: {path}")

# 创建默认最小配置实例
minimal_config = MinimalConfig()

if __name__ == "__main__":
    # 测试配置
    config = MinimalConfig()
    config.print_config()
    
    # 保存配置示例
    config.save_config("./minimal_config.json")
