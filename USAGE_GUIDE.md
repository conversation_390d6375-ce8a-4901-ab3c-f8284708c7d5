# GPU内存优化训练指南

## 🎯 问题解决方案总结

针对你的70GB内存占用问题，我们提供了**只优化当前程序，不影响其他服务**的解决方案。

## 📋 可用脚本说明

### 1. 🔍 **内存检查工具** (推荐首先使用)
```bash
python check_memory_only.py
```
**功能：**
- ✅ 只查看GPU内存状态，不清理任何进程
- ✅ 显示所有GPU进程信息
- ✅ 评估训练可行性
- ✅ 给出配置建议

### 2. 🛡️ **保守训练脚本** (推荐使用)
```bash
python train_conservative.py
```
**功能：**
- ✅ 使用超级保守的配置
- ✅ 只清理当前进程内存
- ✅ 实时监控内存使用
- ✅ 不影响任何其他服务

### 3. 📊 **内存诊断工具**
```bash
python diagnose_memory.py
```
**功能：**
- 测试不同配置的内存使用
- 分析内存占用模式
- 帮助找出70GB占用的原因

### 4. 🔧 **优化后的原始训练脚本**
```bash
python train.py --batch_size 1 --max_seq_length 256
```
**功能：**
- 在原脚本基础上添加了内存优化
- 只清理当前进程内存
- 保持原有功能

## 🚀 推荐使用流程

### 步骤1: 检查当前状态
```bash
python check_memory_only.py
```
这会告诉你：
- 当前GPU内存使用情况
- 哪些进程在占用GPU
- 建议使用什么配置

### 步骤2: 选择合适的训练方式

**如果可用内存 >= 30GB：**
```bash
python train.py --batch_size 1 --max_seq_length 256 --lora_r 32
```

**如果可用内存 15-30GB：**
```bash
python train_conservative.py
```

**如果可用内存 < 15GB：**
```bash
# 需要等待其他任务完成或使用更小模型
python check_memory_only.py  # 先检查状态
```

## ⚙️ 关键优化参数

### 内存影响最大的参数（按重要性排序）

1. **`device_map`** 🔥 最关键
   - 原值: `"auto"`
   - 优化: `"cuda:0"`
   - 影响: 避免模型重复加载

2. **`max_seq_length`** 🔥 关键
   - 原值: `512`
   - 保守: `128`
   - 超保守: `64`

3. **`lora.r`** 🔥 关键
   - 原值: `64`
   - 保守: `16`
   - 超保守: `8`

4. **`batch_size`** 🟡 中等
   - 原值: `2`
   - 优化: `1`

5. **`target_modules`** 🟡 中等
   - 原值: 7个模块
   - 保守: 3个模块
   - 超保守: 2个模块

## 🛡️ 安全原则

### ✅ 我们的脚本会做什么：
- 只清理当前训练进程的GPU缓存
- 优化当前进程的内存使用
- 监控当前进程的内存状态
- 设置环境变量只影响当前进程

### ❌ 我们的脚本不会做什么：
- 不会杀死其他用户的进程
- 不会影响正在运行的推理服务
- 不会清理Jupyter Notebook
- 不会影响其他深度学习应用

## 📊 配置对比表

| 配置级别 | 序列长度 | LoRA秩 | 批次大小 | 预估内存 | 适用场景 |
|---------|---------|--------|----------|----------|----------|
| 标准 | 512 | 64 | 2 | ~25GB | 内存充足 |
| 优化 | 256 | 32 | 1 | ~18GB | 内存中等 |
| 保守 | 128 | 16 | 1 | ~12GB | 内存紧张 |
| 超保守 | 64 | 8 | 1 | ~8GB | 内存极限 |

## 🔧 故障排除

### 如果仍然出现OOM错误：

1. **检查是否有其他大内存进程：**
   ```bash
   python check_memory_only.py
   ```

2. **使用更保守的配置：**
   ```bash
   # 编辑 train_conservative.py，进一步减小参数
   config.data.max_seq_length = 32
   config.lora.r = 4
   ```

3. **考虑使用更小的模型：**
   - Qwen2.5-1.5B-Instruct (更小)
   - 或者等待其他任务完成

### 如果训练速度太慢：

1. **增大梯度累积步数：**
   ```python
   gradient_accumulation_steps = 64  # 或更大
   ```

2. **使用混合精度训练：**
   ```python
   bf16 = True
   tf32 = True
   ```

## 💡 最佳实践

1. **训练前总是先检查：**
   ```bash
   python check_memory_only.py
   ```

2. **从保守配置开始：**
   ```bash
   python train_conservative.py
   ```

3. **逐步调整参数：**
   - 如果内存使用 < 20GB，可以增大参数
   - 如果内存使用 > 50GB，需要减小参数

4. **监控训练过程：**
   - 使用 `watch -n 1 nvidia-smi` 实时监控
   - 注意内存使用趋势

## 🎯 总结

通过这些优化，你可以：
- ✅ 解决70GB内存占用问题
- ✅ 不影响其他正在运行的服务
- ✅ 安全地进行模型训练
- ✅ 根据实际情况调整配置

**推荐操作顺序：**
1. `python check_memory_only.py` - 了解现状
2. `python train_conservative.py` - 开始训练
3. 根据结果调整参数
