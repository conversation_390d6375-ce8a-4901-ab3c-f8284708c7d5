# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型推理脚本
支持单条文本和批量文本审核
"""

import os
import sys
import json
import argparse
import torch
from typing import List, Dict, Any
from pathlib import Path
import logging

from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from utils import setup_logging, get_gpu_info, clear_gpu_cache

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

class TextModerationInference:
    """文本审核推理类"""
    
    def __init__(self, 
                 base_model_path: str,
                 lora_weights_path: str = None,
                 tokenizer_path: str = None,
                 device: str = "auto"):
        """
        初始化推理器
        
        Args:
            base_model_path: 基础模型路径
            lora_weights_path: LoRA权重路径
            tokenizer_path: 分词器路径，默认与base_model_path相同
            device: 设备类型
            
        注意：使用镜像站配置需要通过环境变量设置：
        export HF_ENDPOINT="https://modelscope.cn/api/v1/models"
        export MODELSCOPE_MIRROR=1
        """
        self.base_model_path = base_model_path
        self.lora_weights_path = lora_weights_path
        self.tokenizer_path = tokenizer_path or base_model_path
        self.device = device
        
        self.tokenizer = None
        self.model = None
        
        # 审核标签映射
        self.label_mapping = {
            "合规": 0,
            "违规": 1, 
            "疑似": 2
        }
        self.reverse_label_mapping = {v: k for k, v in self.label_mapping.items()}
        
        # 准备模型加载参数 (镜像配置通过环境变量HF_ENDPOINT和MODELSCOPE_MIRROR自动设置)
        load_kwargs = {
            "trust_remote_code": True,
            "cache_dir": "./hf_cache"
        }
        
        # 检查是否存在镜像配置
        self.use_modelscope = os.environ.get('MODELSCOPE_MIRROR', '0') == '1'
        self.hf_endpoint = os.environ.get('HF_ENDPOINT')
        
        if self.use_modelscope:
            logger.info("使用ModelScope镜像加载模型和分词器")
            # 确保ModelScope模型ID正确性
            if 'Qwen/' in self.base_model_path:
                self.modelscope_path = self.base_model_path.replace('Qwen/', 'qwen/')
                logger.info(f"ModelScope路径转换: {self.base_model_path} -> {self.modelscope_path}")
            else:
                self.modelscope_path = self.base_model_path
        
        # 加载分词器
        try:
            # 根据是否使用ModelScope选择路径
            tok_path = self.modelscope_path if self.use_modelscope else self.tokenizer_path
            logger.info(f"尝试加载分词器：{tok_path}")
            
            # 尝试使用ModelScope SDK加载
            if self.use_modelscope and self.modelscope_path:
                try:
                    from modelscope import snapshot_download
                    model_dir = snapshot_download(self.modelscope_path, cache_dir='./hf_cache')
                    logger.info(f"ModelScope下载成功，模型路径: {model_dir}")
                    self.tokenizer = AutoTokenizer.from_pretrained(model_dir, **load_kwargs)
                except Exception as e:
                    logger.warning(f"ModelScope加载失败：{e}")
                    logger.info("将尝试直接使用transformers加载")
                    self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_path, **load_kwargs)
            else:
                # 正常加载
                self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_path, **load_kwargs)
                
            logger.info("分词器加载成功")
        except Exception as e:
            logger.error(f"分词器加载失败: {e}")
            raise
        
        # 确保有padding token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 添加模型特定参数
        model_kwargs = load_kwargs.copy()
        model_kwargs.update({
            "torch_dtype": torch.bfloat16,  # 使用bfloat16精度
            "device_map": self.device,
            "attn_implementation": "flash_attention_2"  # 使用Flash Attention 2加速
        })
        
        # 加载模型
        try:
            # 如果在分词器加载时使用了ModelScope成功下载了模型
            if hasattr(self, 'model_dir') and self.model_dir:
                model_path = self.model_dir
            else:
                model_path = self.modelscope_path if self.use_modelscope else self.base_model_path
                
                # 尝试使用ModelScope SDK加载
                if self.use_modelscope:
                    try:
                        from modelscope import snapshot_download
                        self.model_dir = snapshot_download(self.modelscope_path, cache_dir='./hf_cache')
                        model_path = self.model_dir
                        logger.info(f"ModelScope下载基础模型成功，路径: {model_path}")
                    except Exception as e:
                        logger.warning(f"ModelScope加载基础模型失败：{e}")
                        logger.info("将尝试直接使用transformers加载")
                        model_path = self.base_model_path
                        
            # 加载基础模型
            logger.info(f"加载基础模型：{model_path}")
            base_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                **model_kwargs
            )
            logger.info("基础模型加载成功。")
            
            # 清理GPU缓存以减少OOM风险
            clear_gpu_cache()
            
            # 加载LoRA权重
            if self.lora_weights_path:
                logger.info(f"加载LoRA权重：{self.lora_weights_path}")
                self.model = PeftModel.from_pretrained(
                    base_model,
                    self.lora_weights_path,
                    torch_dtype=torch.bfloat16
                )
                logger.info("LoRA权重加载成功。")
            else:
                logger.warning("未指定LoRA权重路径，将直接使用基础模型")
                self.model = base_model
                
            # 输出 GPU 信息
            gpu_info = get_gpu_info()
            logger.info(f"GPU内存使用情况: {gpu_info}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
        
        # 合并权重以提升推理速度
        if self.lora_weights_path and hasattr(self.model, 'merge_and_unload'):
            logger.info("合并LoRA权重以提升推理速度...")
            try:
                self.model = self.model.merge_and_unload()
                logger.info("权重合并完成。")
            except Exception as e:
                logger.error(f"合并权重失败: {e}")
        
        # 设置为评估模式
        self.model.eval()
        
        # 输出模型内存使用情况
        gpu_info = get_gpu_info()
        logger.info(f"模型加载完成，当前GPU内存: {gpu_info}")
        logger.info("\n" + "-"*50 + "\n文本审核推理器初始化完成\n" + "-"*50)
        
        logger.info("推理模型加载完成")

    
    def _format_input(self, text: str) -> str:
        """
        格式化输入文本为对话格式
        
        Args:
            text: 待审核文本
            
        Returns:
            格式化后的输入
        """
        system_prompt = """你是一个专业的文本内容审核助手。请对给定的文本内容进行审核，判断其是否符合平台规范。

审核标准：
- 合规：内容健康正面，符合平台规范
- 违规：内容明显违反平台规范，包含有害信息  
- 疑似：内容存在争议或需要进一步人工审核

请仅回答：合规、违规、疑似 中的一个。"""
        
        user_input = f"请审核以下文本内容：\n{text}"
        
        # 使用Qwen2.5的对话格式
        conversation = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
        
        return conversation
    
    def predict_single(self, 
                      text: str,
                      max_new_tokens: int = 10,
                      temperature: float = 0.1,
                      top_p: float = 0.8,
                      do_sample: bool = True) -> Dict[str, Any]:
        """
        单条文本审核预测
        
        Args:
            text: 待审核文本
            max_new_tokens: 最大生成token数
            temperature: 生成温度
            top_p: nucleus sampling参数
            do_sample: 是否采样
            
        Returns:
            预测结果字典
        """
        try:
            # 格式化输入
            formatted_input = self._format_input(text)
            
            # 分词
            inputs = self.tokenizer(
                formatted_input,
                return_tensors="pt",
                truncation=True,
                max_length=2048,
                padding=False
            )
            
            # 移动到设备
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # 生成预测
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=do_sample,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.1
                )
            
            # 解码生成的部分
            generated_ids = outputs[0][inputs['input_ids'].shape[1]:]
            generated_text = self.tokenizer.decode(
                generated_ids,
                skip_special_tokens=True
            ).strip()
            
            # 清理结果
            prediction = self._clean_prediction(generated_text)
            
            # 计算置信度（简单实现）
            confidence = self._calculate_confidence(prediction)
            
            result = {
                "text": text,
                "prediction": prediction,
                "confidence": confidence,
                "raw_output": generated_text,
                "label_id": self.label_mapping.get(prediction, -1)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {
                "text": text,
                "prediction": "错误",
                "confidence": 0.0,
                "raw_output": str(e),
                "label_id": -1
            }
    
    def _clean_prediction(self, raw_output: str) -> str:
        """
        清理预测结果
        
        Args:
            raw_output: 原始输出
            
        Returns:
            清理后的预测结果
        """
        # 移除多余的空白字符
        cleaned = raw_output.strip()
        
        # 检查是否包含有效的审核标签
        for label in ["合规", "违规", "疑似"]:
            if label in cleaned:
                return label
        
        # 如果没有找到有效标签，返回疑似
        return "疑似"
    
    def _calculate_confidence(self, prediction: str) -> float:
        """
        计算预测置信度（简单实现）
        
        Args:
            prediction: 预测结果
            
        Returns:
            置信度分数
        """
        # 简单的置信度计算逻辑
        if prediction in self.label_mapping:
            return 0.9  # 有效预测的基础置信度
        else:
            return 0.1  # 无效预测的低置信度
    
    def predict_batch(self, 
                     texts: List[str],
                     batch_size: int = 8,
                     **kwargs) -> List[Dict[str, Any]]:
        """
        批量文本审核预测
        
        Args:
            texts: 文本列表
            batch_size: 批次大小
            **kwargs: 其他预测参数
            
        Returns:
            预测结果列表
        """
        results = []
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        logger.info(f"开始批量预测，总计 {len(texts)} 条文本，分 {total_batches} 个批次")
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            batch_results = []
            
            logger.info(f"处理批次 {i//batch_size + 1}/{total_batches}")
            
            for text in batch_texts:
                result = self.predict_single(text, **kwargs)
                batch_results.append(result)
            
            results.extend(batch_results)
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        logger.info("批量预测完成")
        return results
    
    def evaluate_on_dataset(self, test_file: str) -> Dict[str, Any]:
        """
        在测试数据集上评估模型
        
        Args:
            test_file: 测试文件路径
            
        Returns:
            评估结果
        """
        import jsonlines
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
        
        logger.info(f"开始评估模型，测试文件: {test_file}")
        
        # 加载测试数据
        test_data = []
        with jsonlines.open(test_file, 'r') as reader:
            for item in reader:
                test_data.append(item)
        
        logger.info(f"加载测试数据: {len(test_data)} 条样本")
        
        # 提取文本和标签
        texts = [item['text'] for item in test_data]
        true_labels = [item['label'] for item in test_data]
        
        # 批量预测
        predictions = self.predict_batch(texts)
        pred_labels = [pred['prediction'] for pred in predictions]
        
        # 计算评估指标
        accuracy = accuracy_score(true_labels, pred_labels)
        
        # 分类报告
        report = classification_report(
            true_labels, 
            pred_labels, 
            target_names=list(self.label_mapping.keys()),
            output_dict=True
        )
        
        # 混淆矩阵
        cm = confusion_matrix(
            true_labels, 
            pred_labels, 
            labels=list(self.label_mapping.keys())
        )
        
        evaluation_result = {
            "accuracy": accuracy,
            "classification_report": report,
            "confusion_matrix": cm.tolist(),
            "predictions": predictions,
            "total_samples": len(test_data)
        }
        
        logger.info(f"评估完成，准确率: {accuracy:.4f}")
        
        return evaluation_result
    
    def save_predictions(self, predictions: List[Dict[str, Any]], output_file: str):
        """
        保存预测结果
        
        Args:
            predictions: 预测结果列表
            output_file: 输出文件路径
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(predictions, f, indent=2, ensure_ascii=False)
        
        logger.info(f"预测结果已保存到: {output_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Qwen2.5文本审核模型推理")
    
    parser.add_argument("--base_model", type=str, required=True,
                       help="基础模型路径")
    parser.add_argument("--lora_weights", type=str, required=True,
                       help="LoRA权重路径")
    parser.add_argument("--tokenizer", type=str, default=None,
                       help="分词器路径")
    
    # 推理模式选择
    parser.add_argument("--mode", type=str, choices=["single", "batch", "evaluate"],
                       default="single", help="推理模式")
    
    # 单条文本推理
    parser.add_argument("--text", type=str, default=None,
                       help="待审核的单条文本")
    
    # 批量推理
    parser.add_argument("--input_file", type=str, default=None,
                       help="批量推理输入文件")
    parser.add_argument("--output_file", type=str, default="predictions.json",
                       help="预测结果输出文件")
    
    # 评估模式
    parser.add_argument("--test_file", type=str, default=None,
                       help="测试数据文件")
    parser.add_argument("--eval_output", type=str, default="evaluation_result.json",
                       help="评估结果输出文件")
    
    # 生成参数
    parser.add_argument("--max_new_tokens", type=int, default=10,
                       help="最大生成token数")
    parser.add_argument("--temperature", type=float, default=0.1,
                       help="生成温度")
    parser.add_argument("--batch_size", type=int, default=8,
                       help="批量推理批次大小")
    
    args = parser.parse_args()
    
    # 打印GPU信息
    get_gpu_info()
    
    # 创建推理器
    logger.info("初始化推理器...")
    inference = TextModerationInference(
        base_model_path=args.base_model,
        lora_weights_path=args.lora_weights,
        tokenizer_path=args.tokenizer
    )
    
    # 根据模式执行不同的推理任务
    if args.mode == "single":
        # 单条文本推理
        if not args.text:
            # 交互式输入
            print("\n文本审核系统 - 交互模式")
            print("输入 'quit' 退出")
            print("="*50)
            
            while True:
                text = input("\n请输入待审核文本: ").strip()
                
                if text.lower() == 'quit':
                    break
                
                if not text:
                    continue
                
                result = inference.predict_single(
                    text,
                    max_new_tokens=args.max_new_tokens,
                    temperature=args.temperature
                )
                
                print(f"\n审核结果: {result['prediction']}")
                print(f"置信度: {result['confidence']:.2f}")
                if result['raw_output'] != result['prediction']:
                    print(f"原始输出: {result['raw_output']}")
        else:
            # 命令行指定文本
            result = inference.predict_single(
                args.text,
                max_new_tokens=args.max_new_tokens,
                temperature=args.temperature
            )
            
            print(f"\n待审核文本: {args.text}")
            print(f"审核结果: {result['prediction']}")
            print(f"置信度: {result['confidence']:.2f}")
    
    elif args.mode == "batch":
        # 批量推理
        if not args.input_file:
            logger.error("批量推理模式需要指定输入文件")
            return
        
        # 加载输入数据
        import jsonlines
        texts = []
        
        with jsonlines.open(args.input_file, 'r') as reader:
            for item in reader:
                if isinstance(item, dict) and 'text' in item:
                    texts.append(item['text'])
                elif isinstance(item, str):
                    texts.append(item)
        
        logger.info(f"加载 {len(texts)} 条文本进行批量推理")
        
        # 批量预测
        predictions = inference.predict_batch(
            texts,
            batch_size=args.batch_size,
            max_new_tokens=args.max_new_tokens,
            temperature=args.temperature
        )
        
        # 保存结果
        inference.save_predictions(predictions, args.output_file)
        
        # 打印统计信息
        pred_counts = {}
        for pred in predictions:
            label = pred['prediction']
            pred_counts[label] = pred_counts.get(label, 0) + 1
        
        print("\n预测结果统计:")
        for label, count in pred_counts.items():
            print(f"  {label}: {count} ({count/len(predictions)*100:.1f}%)")
    
    elif args.mode == "evaluate":
        # 评估模式
        if not args.test_file:
            logger.error("评估模式需要指定测试文件")
            return
        
        # 在测试集上评估
        eval_result = inference.evaluate_on_dataset(args.test_file)
        
        # 保存评估结果
        with open(args.eval_output, 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            eval_result_json = eval_result.copy()
            json.dump(eval_result_json, f, indent=2, ensure_ascii=False)
        
        logger.info(f"评估结果已保存到: {args.eval_output}")
        
        # 打印评估结果
        print(f"\n评估结果:")
        print(f"准确率: {eval_result['accuracy']:.4f}")
        print(f"总样本数: {eval_result['total_samples']}")
        
        # 打印分类报告
        report = eval_result['classification_report']
        print(f"\n各类别性能:")
        for label in ['合规', '违规', '疑似']:
            if label in report:
                metrics = report[label]
                print(f"  {label}:")
                print(f"    精确率: {metrics['precision']:.4f}")
                print(f"    召回率: {metrics['recall']:.4f}")
                print(f"    F1分数: {metrics['f1-score']:.4f}")
    
    # 清理GPU缓存
    clear_gpu_cache()
    logger.info("推理完成")

if __name__ == "__main__":
    main()
