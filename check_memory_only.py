#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存检查工具 - 只查看，不清理任何进程
帮助了解当前GPU内存使用情况，不影响任何服务
"""

import subprocess
import psutil
import torch
import os

def check_gpu_status():
    """检查GPU状态"""
    print("GPU硬件信息:")
    print("="*50)
    
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            total_memory = props.total_memory / 1024**3
            
            print(f"\nGPU {i}: {props.name}")
            print(f"  总内存: {total_memory:.1f} GB")
            print(f"  多处理器数量: {props.multi_processor_count}")
            print(f"  CUDA计算能力: {props.major}.{props.minor}")
    else:
        print("未检测到CUDA GPU")

def check_current_process_memory():
    """检查当前进程的内存使用"""
    print("\n当前进程内存使用:")
    print("="*50)
    
    # 系统内存
    process = psutil.Process()
    memory_info = process.memory_info()
    
    print(f"当前进程 PID: {process.pid}")
    print(f"RSS内存: {memory_info.rss / 1024**3:.2f} GB")
    print(f"VMS内存: {memory_info.vms / 1024**3:.2f} GB")
    print(f"内存百分比: {process.memory_percent():.1f}%")
    
    # GPU内存
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        max_allocated = torch.cuda.max_memory_allocated() / 1024**3
        
        print(f"GPU内存 (当前进程):")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  已保留: {reserved:.2f} GB")
        print(f"  峰值分配: {max_allocated:.2f} GB")

def check_all_gpu_processes():
    """查看所有GPU进程（只查看，不操作）"""
    print("\n所有GPU进程:")
    print("="*50)
    
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-compute-apps=pid,process_name,used_memory', 
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            current_pid = os.getpid()
            total_gpu_memory = 0
            
            print(f"{'PID':<8} {'进程名':<20} {'内存(MB)':<10} {'用户':<15} {'状态'}")
            print("-" * 70)
            
            for line in lines:
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        pid = int(parts[0])
                        process_name = parts[1]
                        memory_mb = int(parts[2])
                        total_gpu_memory += memory_mb
                        
                        # 获取进程用户信息
                        try:
                            proc = psutil.Process(pid)
                            user = proc.username()
                            status = "当前进程" if pid == current_pid else "其他进程"
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            user = "unknown"
                            status = "无法访问"
                        
                        print(f"{pid:<8} {process_name:<20} {memory_mb:<10} {user:<15} {status}")
            
            print("-" * 70)
            print(f"总GPU内存使用: {total_gpu_memory} MB ({total_gpu_memory/1024:.1f} GB)")
            
        else:
            print("未发现GPU进程或nvidia-smi命令失败")
            
    except Exception as e:
        print(f"检查GPU进程失败: {e}")

def check_system_resources():
    """检查系统资源"""
    print("\n系统资源状态:")
    print("="*50)
    
    # CPU信息
    cpu_count = psutil.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"CPU:")
    print(f"  核心数: {cpu_count}")
    print(f"  使用率: {cpu_percent:.1f}%")
    
    # 内存信息
    memory = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    print(f"系统内存:")
    print(f"  总计: {memory.total / 1024**3:.1f} GB")
    print(f"  可用: {memory.available / 1024**3:.1f} GB")
    print(f"  使用率: {memory.percent:.1f}%")
    
    print(f"交换空间:")
    print(f"  总计: {swap.total / 1024**3:.1f} GB")
    print(f"  使用: {swap.used / 1024**3:.1f} GB")
    print(f"  使用率: {swap.percent:.1f}%")

def estimate_training_feasibility():
    """评估训练可行性"""
    print("\n训练可行性评估:")
    print("="*50)
    
    if not torch.cuda.is_available():
        print("❌ 无CUDA GPU，无法进行GPU训练")
        return
    
    # 获取GPU总内存
    total_gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    # 获取当前GPU使用情况
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=memory.used', 
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            used_memory_mb = int(result.stdout.strip())
            used_memory_gb = used_memory_mb / 1024
            available_memory = total_gpu_memory - used_memory_gb
        else:
            available_memory = total_gpu_memory
            
    except:
        available_memory = total_gpu_memory
    
    print(f"GPU内存分析:")
    print(f"  总内存: {total_gpu_memory:.1f} GB")
    print(f"  估计可用: {available_memory:.1f} GB")
    
    # 评估不同配置的可行性
    print(f"\n配置建议:")
    
    if available_memory >= 50:
        print("✅ 内存充足，可以使用标准配置:")
        print("   - 序列长度: 512")
        print("   - LoRA秩: 64")
        print("   - 批次大小: 2")
        
    elif available_memory >= 30:
        print("🟡 内存中等，建议使用优化配置:")
        print("   - 序列长度: 256")
        print("   - LoRA秩: 32")
        print("   - 批次大小: 1")
        
    elif available_memory >= 15:
        print("🟠 内存紧张，需要使用最小配置:")
        print("   - 序列长度: 128")
        print("   - LoRA秩: 16")
        print("   - 批次大小: 1")
        print("   - 梯度累积: 32+")
        
    else:
        print("❌ 内存不足，建议:")
        print("   - 等待其他任务完成")
        print("   - 使用更小的模型")
        print("   - 考虑CPU训练（很慢）")

def main():
    """主函数"""
    print("GPU内存状态检查工具")
    print("="*60)
    print("注意: 此工具只查看状态，不会清理任何进程")
    print("="*60)
    
    # 检查各项状态
    check_gpu_status()
    check_current_process_memory()
    check_all_gpu_processes()
    check_system_resources()
    estimate_training_feasibility()
    
    print("\n" + "="*60)
    print("检查完成")
    print("="*60)
    
    print("\n💡 使用建议:")
    print("1. 如果发现大量内存被占用，可以:")
    print("   - 检查是否有不需要的Jupyter Notebook")
    print("   - 查看是否有卡住的训练进程")
    print("   - 联系相关用户确认是否可以释放资源")
    print("2. 根据可用内存选择合适的训练配置")
    print("3. 使用 train_conservative.py 进行保守训练")

if __name__ == "__main__":
    main()
