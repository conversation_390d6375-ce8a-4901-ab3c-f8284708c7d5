# GPU内存不足问题修复指南

## 问题分析

根据错误信息分析，主要问题是：

```
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 896.00 MiB. 
GPU 0 has a total capacity of 79.33 GiB of which 715.06 MiB is free. 
Process 83906 has 73.49 GiB memory in use.
```

**核心问题：**
1. GPU总容量79.33GB，但进程83906占用了73.49GB
2. 可用内存只有715MB，无法分配896MB
3. 可能存在内存泄漏或多个训练进程同时运行

## 已实施的修复措施

### 1. 配置优化 (config.py)

**批次大小优化：**
- `per_device_train_batch_size`: 2 → 1
- `per_device_eval_batch_size`: 4 → 1  
- `gradient_accumulation_steps`: 8 → 16 (保持有效批次大小)

**工作进程优化：**
- `dataloader_num_workers`: 4 → 0
- `preprocessing_num_workers`: 4 → 1

### 2. 模型训练器优化 (model_trainer.py)

**内存管理增强：**
- 添加了`clear_gpu_memory()`和`print_gpu_memory_usage()`函数
- 在关键步骤后清理GPU缓存
- 设置`PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True`
- 模型加载时启用`low_cpu_mem_usage=True`和`use_cache=False`

**监控功能：**
- 实时GPU内存使用监控
- 训练过程中的内存状态打印

### 3. 数据处理优化 (data_processor.py)

**内存效率提升：**
- 分词时使用动态padding而非固定长度padding
- 减小批处理大小到100
- 优化数据加载流程

### 4. 新增工具脚本

**内存优化训练脚本 (train_memory_optimized.py)：**
- 自动检测和清理GPU进程
- 进一步优化的配置参数
- 系统资源检查
- 更小的LoRA配置 (r=32)
- 更短的序列长度 (256)

**GPU清理工具 (clear_gpu.py)：**
- 检测占用GPU的进程
- 选择性或批量清理进程
- GPU内存状态监控

## 使用建议

### 1. 训练前清理GPU

```bash
# 运行GPU清理工具
python clear_gpu.py

# 或者手动清理
nvidia-smi
# 找到占用GPU的进程PID，然后：
kill -9 <PID>
```

### 2. 使用优化的训练脚本

```bash
# 使用内存优化的训练脚本
python train_memory_optimized.py
```

### 3. 如果仍然内存不足

**进一步减小参数：**

```python
# 在train_memory_optimized.py中进一步调整
config.data.max_seq_length = 128  # 更短序列
config.lora.r = 16  # 更小LoRA秩
config.training.gradient_accumulation_steps = 64  # 更大梯度累积
```

**使用梯度检查点：**
```python
# 在TrainingArguments中添加
gradient_checkpointing=True
```

### 4. 监控内存使用

```bash
# 实时监控GPU使用
watch -n 1 nvidia-smi

# 或者使用Python监控
python -c "
import torch
if torch.cuda.is_available():
    print(f'GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB / {torch.cuda.get_device_properties(0).total_memory/1024**3:.2f}GB')
"
```

## 参数调优建议

### 内存使用优先级（从高到低）

1. **序列长度** (`max_seq_length`)
   - 512 → 256 → 128
   - 影响最大，建议优先调整

2. **批次大小** (`per_device_train_batch_size`)
   - 2 → 1 → 使用梯度累积

3. **LoRA秩** (`lora.r`)
   - 64 → 32 → 16 → 8

4. **目标模块数量** (`target_modules`)
   - 减少LoRA应用的层数

### 性能平衡

- **有效批次大小** = `batch_size × gradient_accumulation_steps`
- 建议保持有效批次大小在16-32之间
- 可以通过增大`gradient_accumulation_steps`来补偿小批次大小

## 故障排除

### 如果问题持续存在

1. **检查其他进程：**
   ```bash
   ps aux | grep python
   nvidia-smi
   ```

2. **重启CUDA服务：**
   ```bash
   sudo systemctl restart nvidia-persistenced
   ```

3. **检查系统内存：**
   ```bash
   free -h
   htop
   ```

4. **使用更激进的配置：**
   - 序列长度降到64-128
   - LoRA秩降到8-16
   - 考虑使用量化训练

### 紧急情况处理

如果训练过程中出现OOM：

```bash
# 立即清理所有GPU进程
sudo pkill -f python
nvidia-smi --gpu-reset

# 清理系统缓存
sync && echo 3 > /proc/sys/vm/drop_caches
```

## 预防措施

1. **训练前检查：**
   - 运行`clear_gpu.py`清理GPU
   - 检查系统资源使用情况
   - 确认没有其他训练任务运行

2. **监控设置：**
   - 使用`watch nvidia-smi`实时监控
   - 设置内存使用告警

3. **渐进式调参：**
   - 从最保守的参数开始
   - 逐步增大参数直到接近内存限制
   - 保留10-20%的内存余量

通过这些修复措施，应该能够解决GPU内存不足的问题。建议先使用`train_memory_optimized.py`脚本进行训练。
