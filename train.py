# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型训练主脚本
支持命令行参数配置和完整的训练流程
"""

import os
import sys
import argparse
import json
import logging
from pathlib import Path
from datetime import datetime

# 本地模块导入
from config import Config
from model_trainer import QwenModerationTrainer
from utils import setup_logging, get_gpu_info, get_system_info

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Qwen2.5文本审核模型微调训练")
    
    # 基础配置
    parser.add_argument("--config", type=str, default=None,
                       help="配置文件路径")
    parser.add_argument("--output_dir", type=str, default="./output",
                       help="输出目录")
    parser.add_argument("--run_name", type=str, default="qwen2.5-text-moderation",
                       help="运行名称")
    
    # 模型相关参数
    parser.add_argument("--model", type=str, help="模型名称或路径")
    parser.add_argument("--model_revision", type=str, help="模型版本")
    parser.add_argument("--torch_dtype", type=str, choices=["float16", "bfloat16", "float32"], help="模型精度类型")
    
    # LoRA配置
    parser.add_argument("--lora_r", type=int, default=64,
                       help="LoRA秩")
    parser.add_argument("--lora_alpha", type=int, default=128,
                       help="LoRA alpha参数")
    parser.add_argument("--lora_dropout", type=float, default=0.1,
                       help="LoRA dropout率")
    
    # 训练配置
    parser.add_argument("--num_epochs", type=int, default=3,
                       help="训练轮数")
    parser.add_argument("--batch_size", type=int, default=2,
                       help="训练批次大小")
    parser.add_argument("--eval_batch_size", type=int, default=4,
                       help="评估批次大小")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=8,
                       help="梯度累积步数")
    parser.add_argument("--learning_rate", type=float, default=2e-4,
                       help="学习率")
    parser.add_argument("--weight_decay", type=float, default=0.01,
                       help="权重衰减")
    parser.add_argument("--warmup_ratio", type=float, default=0.1,
                       help="预热比例")
    
    # 数据配置
    parser.add_argument("--train_file", type=str, default="./data/train.jsonl",
                       help="训练数据文件")
    parser.add_argument("--validation_file", type=str, default="./data/validation.jsonl",
                       help="验证数据文件")
    parser.add_argument("--test_file", type=str, default="./data/test.jsonl",
                       help="测试数据文件")
    parser.add_argument("--max_seq_length", type=int, default=2048,
                       help="最大序列长度")
    
    # 保存和日志配置
    parser.add_argument("--save_steps", type=int, default=500,
                       help="保存步数间隔")
    parser.add_argument("--eval_steps", type=int, default=500,
                       help="评估步数间隔")
    parser.add_argument("--logging_steps", type=int, default=10,
                       help="日志步数间隔")
    parser.add_argument("--save_total_limit", type=int, default=3,
                       help="最大保存检查点数量")
    
    # 系统配置
    parser.add_argument("--cuda_visible_devices", type=str, default="0",
                       help="可见GPU设备")
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")
    
    # 其他选项
    parser.add_argument("--resume_from_checkpoint", type=str, default=None,
                       help="从检查点恢复训练")
    parser.add_argument("--create_sample_data", action="store_true",
                       help="创建示例数据")
    parser.add_argument("--sample_data_size", type=int, default=1000,
                       help="示例数据大小")
    parser.add_argument("--dry_run", action="store_true",
                       help="干运行模式，只检查配置不实际训练")
    
    return parser.parse_args()

def update_config_from_args(config: Config, args):
    """根据命令行参数更新配置"""
    
    # 模型配置
    if args.model is not None:
        config.model.model_name_or_path = args.model
    if args.torch_dtype is not None:
        config.model.torch_dtype = args.torch_dtype
    
    # LoRA配置
    config.lora.r = args.lora_r
    config.lora.lora_alpha = args.lora_alpha
    config.lora.lora_dropout = args.lora_dropout
    
    # 训练配置
    config.training.output_dir = args.output_dir
    config.training.run_name = args.run_name
    config.training.num_train_epochs = args.num_epochs
    config.training.per_device_train_batch_size = args.batch_size
    config.training.per_device_eval_batch_size = args.eval_batch_size
    config.training.gradient_accumulation_steps = args.gradient_accumulation_steps
    config.training.learning_rate = args.learning_rate
    config.training.weight_decay = args.weight_decay
    config.training.warmup_ratio = args.warmup_ratio
    config.training.save_steps = args.save_steps
    config.training.eval_steps = args.eval_steps
    config.training.logging_steps = args.logging_steps
    config.training.save_total_limit = args.save_total_limit
    config.training.seed = args.seed
    
    # 数据配置
    config.data.train_file = args.train_file
    config.data.validation_file = args.validation_file
    config.data.test_file = args.test_file
    config.data.max_seq_length = args.max_seq_length
    
    # 系统配置
    config.system.cuda_visible_devices = args.cuda_visible_devices

def validate_config(config: Config):
    """验证配置的有效性"""
    errors = []
    
    # 检查输出目录
    try:
        Path(config.training.output_dir).mkdir(parents=True, exist_ok=True)
    except Exception as e:
        errors.append(f"无法创建输出目录 {config.training.output_dir}: {e}")
    
    # 检查LoRA参数
    if config.lora.r <= 0:
        errors.append("LoRA秩必须大于0")
    
    if config.lora.lora_alpha <= 0:
        errors.append("LoRA alpha必须大于0")
    
    if not (0 <= config.lora.lora_dropout <= 1):
        errors.append("LoRA dropout必须在0-1之间")
    
    # 检查训练参数
    if config.training.num_train_epochs <= 0:
        errors.append("训练轮数必须大于0")
    
    if config.training.per_device_train_batch_size <= 0:
        errors.append("训练批次大小必须大于0")
    
    if config.training.learning_rate <= 0:
        errors.append("学习率必须大于0")
    
    # 检查数据文件（如果不存在会自动创建示例数据）
    data_files = [
        config.data.train_file,
        config.data.validation_file,
        config.data.test_file
    ]
    
    missing_files = [f for f in data_files if not Path(f).exists()]
    if missing_files:
        print(f"警告: 以下数据文件不存在，将创建示例数据: {missing_files}")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))

def print_training_info(config: Config):
    """打印训练信息"""
    print("\n" + "="*60)
    print("Qwen2.5文本审核模型微调训练")
    print("="*60)
    
    print(f"\n[模型配置]")
    print(f"基础模型: {config.model.model_name_or_path}")
    print(f"模型精度: {config.model.torch_dtype}")
    print(f"注意力实现: {config.model.attn_implementation}")
    
    print(f"\n[LoRA配置]")
    print(f"LoRA秩: {config.lora.r}")
    print(f"LoRA alpha: {config.lora.lora_alpha}")
    print(f"LoRA dropout: {config.lora.lora_dropout}")
    print(f"目标模块: {', '.join(config.lora.target_modules)}")
    
    print(f"\n[训练配置]")
    print(f"训练轮数: {config.training.num_train_epochs}")
    print(f"批次大小: {config.training.per_device_train_batch_size}")
    print(f"梯度累积: {config.training.gradient_accumulation_steps}")
    print(f"有效批次大小: {config.training.per_device_train_batch_size * config.training.gradient_accumulation_steps}")
    print(f"学习率: {config.training.learning_rate}")
    print(f"权重衰减: {config.training.weight_decay}")
    print(f"预热比例: {config.training.warmup_ratio}")
    
    print(f"\n[数据配置]")
    print(f"训练数据: {config.data.train_file}")
    print(f"验证数据: {config.data.validation_file}")
    print(f"测试数据: {config.data.test_file}")
    print(f"最大序列长度: {config.data.max_seq_length}")
    
    print(f"\n[输出配置]")
    print(f"输出目录: {config.training.output_dir}")
    print(f"运行名称: {config.training.run_name}")
    print(f"保存间隔: {config.training.save_steps} 步")
    print(f"评估间隔: {config.training.eval_steps} 步")
    
    print("="*60)

def main():
    """主函数"""
    # 设置内存优化环境变量（只影响当前进程）
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True,max_split_size_mb:128"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    # 解析命令行参数
    args = parse_arguments()

    # 设置日志
    log_file = Path(args.output_dir) / "training.log"
    setup_logging(log_file=str(log_file))
    logger = logging.getLogger(__name__)

    logger.info("开始Qwen2.5文本审核模型微调训练")
    logger.info(f"命令行参数: {vars(args)}")

    # 检查GPU内存状态（不清理其他进程）
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        # 只清理当前进程的缓存
        torch.cuda.empty_cache()
        allocated = torch.cuda.memory_allocated() / 1024**3
        logger.info(f"GPU总内存: {total_memory:.1f} GB, 当前进程使用: {allocated:.2f} GB")

    try:
        # 加载配置
        if args.config and Path(args.config).exists():
            logger.info(f"从文件加载配置: {args.config}")
            config = Config()
            config.load_config(args.config)
        else:
            logger.info("使用默认配置")
            config = Config()
        
        # 根据命令行参数更新配置
        update_config_from_args(config, args)
        
        # 验证配置
        validate_config(config)
        
        # 保存最终配置
        config_file = Path(args.output_dir) / "final_config.json"
        config.save_config(str(config_file))
        logger.info(f"最终配置已保存到: {config_file}")
        
        # 打印训练信息
        print_training_info(config)
        
        # 获取系统信息
        get_system_info()
        get_gpu_info()
        
        # 创建示例数据（如果需要）
        if args.create_sample_data:
            logger.info("创建示例数据...")
            from data_processor import TextModerationDataProcessor
            from transformers import AutoTokenizer
            
            tokenizer = AutoTokenizer.from_pretrained(
                config.model.model_name_or_path,
                trust_remote_code=True
            )
            
            processor = TextModerationDataProcessor(tokenizer, config)
            processor.create_sample_data(
                output_dir=str(Path(config.data.train_file).parent),
                num_samples=args.sample_data_size
            )
            logger.info("示例数据创建完成")
        
        # 干运行模式
        if args.dry_run:
            logger.info("干运行模式，配置检查完成，退出")
            print("\n配置检查通过！")
            print("如需开始训练，请移除 --dry_run 参数")
            return
        
        # 创建训练器
        logger.info("初始化训练器...")
        trainer = QwenModerationTrainer(config)
        
        # 检查是否从检查点恢复
        if args.resume_from_checkpoint:
            logger.info(f"从检查点恢复训练: {args.resume_from_checkpoint}")
            # 这里可以添加检查点恢复逻辑
        
        # 开始训练
        logger.info("开始训练流程...")
        trainer.run_full_training()
        
        # 训练完成
        logger.info("训练流程完成！")
        
        print(f"\n训练完成！")
        print(f"模型保存位置: {config.training.output_dir}")
        print(f"LoRA权重: {config.training.output_dir}/lora_weights")
        print(f"训练日志: {log_file}")
        
        print(f"\n使用推理脚本测试模型:")
        print(f"python inference.py \\")
        print(f"  --base_model {config.model.model_name_or_path} \\")
        print(f"  --lora_weights {config.training.output_dir}/lora_weights \\")
        print(f"  --mode single \\")
        print(f"  --text '测试文本内容'")
        
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        print("\n训练被中断")
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        print(f"\n训练失败: {e}")
        raise
    finally:
        # 清理GPU缓存
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == "__main__":
    main()
