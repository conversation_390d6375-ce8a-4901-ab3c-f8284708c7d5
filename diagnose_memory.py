#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存使用诊断脚本
分析为什么训练会占用70多GB内存
"""

import os
import gc
import torch
import psutil
import subprocess
from transformers import AutoTokenizer, AutoModelForCausalLM
from config import Config

def get_process_memory():
    """获取当前进程内存使用"""
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        'rss': memory_info.rss / 1024**3,  # GB
        'vms': memory_info.vms / 1024**3,  # GB
        'percent': process.memory_percent()
    }

def get_gpu_memory():
    """获取GPU内存使用"""
    if not torch.cuda.is_available():
        return None
    
    allocated = torch.cuda.memory_allocated() / 1024**3
    reserved = torch.cuda.memory_reserved() / 1024**3
    max_allocated = torch.cuda.max_memory_allocated() / 1024**3
    total = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    return {
        'allocated': allocated,
        'reserved': reserved,
        'max_allocated': max_allocated,
        'total': total,
        'free': total - reserved
    }

def print_memory_status(step_name):
    """打印内存状态"""
    print(f"\n{'='*20} {step_name} {'='*20}")
    
    # 进程内存
    proc_mem = get_process_memory()
    print(f"进程内存:")
    print(f"  RSS: {proc_mem['rss']:.2f} GB")
    print(f"  VMS: {proc_mem['vms']:.2f} GB")
    print(f"  百分比: {proc_mem['percent']:.1f}%")
    
    # GPU内存
    gpu_mem = get_gpu_memory()
    if gpu_mem:
        print(f"GPU内存:")
        print(f"  已分配: {gpu_mem['allocated']:.2f} GB")
        print(f"  已保留: {gpu_mem['reserved']:.2f} GB")
        print(f"  峰值: {gpu_mem['max_allocated']:.2f} GB")
        print(f"  总计: {gpu_mem['total']:.2f} GB")
        print(f"  可用: {gpu_mem['free']:.2f} GB")
    else:
        print("GPU内存: 不可用")

def test_model_loading():
    """测试模型加载的内存使用"""
    print("开始模型加载内存测试...")
    
    config = Config()
    print_memory_status("初始状态")
    
    # 测试分词器加载
    print("\n加载分词器...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            config.model.model_name_or_path,
            trust_remote_code=True,
            cache_dir=config.model.cache_dir
        )
        print_memory_status("分词器加载后")
    except Exception as e:
        print(f"分词器加载失败: {e}")
        return
    
    # 测试模型加载 - 使用不同的device_map设置
    device_maps = ["cuda:0", "auto", {"": 0}]
    
    for device_map in device_maps:
        print(f"\n测试 device_map={device_map}")
        
        # 清理内存
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
        
        try:
            model = AutoModelForCausalLM.from_pretrained(
                config.model.model_name_or_path,
                torch_dtype=torch.bfloat16,
                device_map=device_map,
                trust_remote_code=True,
                cache_dir=config.model.cache_dir,
                low_cpu_mem_usage=True,
                use_cache=False
            )
            
            print_memory_status(f"模型加载后 (device_map={device_map})")
            
            # 计算模型参数量
            total_params = sum(p.numel() for p in model.parameters())
            param_size_gb = total_params * 2 / 1024**3  # bfloat16 = 2 bytes
            print(f"模型参数量: {total_params:,}")
            print(f"理论内存需求: {param_size_gb:.2f} GB")
            
            # 删除模型
            del model
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            print_memory_status(f"模型删除后 (device_map={device_map})")
            
        except Exception as e:
            print(f"模型加载失败 (device_map={device_map}): {e}")
    
    # 清理分词器
    del tokenizer
    gc.collect()
    print_memory_status("全部清理后")

def test_data_processing():
    """测试数据处理的内存使用"""
    print("\n开始数据处理内存测试...")
    
    config = Config()
    print_memory_status("数据处理开始前")
    
    try:
        from data_processor import TextModerationDataProcessor
        
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            config.model.model_name_or_path,
            trust_remote_code=True
        )
        print_memory_status("分词器加载后")
        
        # 创建数据处理器
        processor = TextModerationDataProcessor(tokenizer, config)
        print_memory_status("数据处理器创建后")
        
        # 创建示例数据
        processor.create_sample_data(num_samples=100)  # 减少样本数
        print_memory_status("示例数据创建后")
        
        # 准备数据集
        datasets = processor.prepare_datasets()
        print_memory_status("数据集准备后")
        
        # 清理
        del datasets
        del processor
        del tokenizer
        gc.collect()
        print_memory_status("数据处理清理后")
        
    except Exception as e:
        print(f"数据处理测试失败: {e}")

def check_system_limits():
    """检查系统限制"""
    print("\n系统限制检查:")
    
    # 检查ulimit
    try:
        result = subprocess.run(['ulimit', '-v'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"虚拟内存限制: {result.stdout.strip()}")
    except:
        pass
    
    # 检查系统内存
    memory = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    print(f"系统总内存: {memory.total / 1024**3:.1f} GB")
    print(f"系统可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"交换空间总计: {swap.total / 1024**3:.1f} GB")
    print(f"交换空间使用: {swap.used / 1024**3:.1f} GB")
    
    # 检查GPU
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"GPU {i}: {props.name}, 内存: {props.total_memory / 1024**3:.1f} GB")

def main():
    """主函数"""
    print("内存使用诊断工具")
    print("="*60)
    
    # 检查系统限制
    check_system_limits()
    
    # 测试模型加载
    test_model_loading()
    
    # 测试数据处理
    test_data_processing()
    
    print("\n" + "="*60)
    print("诊断完成")
    print("="*60)
    
    print("\n分析结论:")
    print("1. 如果模型加载就占用70GB+，问题在于device_map设置")
    print("2. 如果数据处理占用大量内存，需要优化数据处理流程")
    print("3. 检查是否有内存泄漏或重复加载")
    print("4. 考虑使用更小的模型或量化")

if __name__ == "__main__":
    main()
