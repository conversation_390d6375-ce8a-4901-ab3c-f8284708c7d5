#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型演示脚本
展示完整的训练和推理流程
"""

import os
import sys
import json
import time
from pathlib import Path
import logging

# 本地模块导入
from config import Config
from data_processor import TextModerationDataProcessor
from model_trainer import QwenModerationTrainer
from utils import setup_logging, get_gpu_info, ModelInference

def demo_config():
    """演示配置管理"""
    print("\n" + "="*50)
    print("1. 配置管理演示")
    print("="*50)
    
    # 创建配置
    config = Config()
    
    # 显示默认配置
    print("默认配置:")
    config.print_config()
    
    # 修改配置
    config.update_config(
        training={
            "num_train_epochs": 2,
            "per_device_train_batch_size": 1,
            "learning_rate": 1e-4
        },
        lora={
            "r": 32,
            "lora_alpha": 64
        }
    )
    
    print("\n修改后的配置:")
    print(f"训练轮数: {config.training.num_train_epochs}")
    print(f"批次大小: {config.training.per_device_train_batch_size}")
    print(f"学习率: {config.training.learning_rate}")
    print(f"LoRA秩: {config.lora.r}")
    print(f"LoRA alpha: {config.lora.lora_alpha}")
    
    # 保存配置
    config.save_config("./demo_config.json")
    print("\n配置已保存到 demo_config.json")

def demo_data_processing():
    """演示数据处理"""
    print("\n" + "="*50)
    print("2. 数据处理演示")
    print("="*50)
    
    from transformers import AutoTokenizer
    
    # 加载配置和分词器
    config = Config()
    
    print("加载分词器...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen2.5-7B-Instruct",
            trust_remote_code=True,
            cache_dir="./hf_cache"
        )
        print("分词器加载成功")
    except Exception as e:
        print(f"分词器加载失败: {e}")
        print("使用模拟分词器进行演示...")
        return
    
    # 创建数据处理器
    processor = TextModerationDataProcessor(tokenizer, config)
    
    # 创建示例数据
    print("\n创建示例数据...")
    processor.create_sample_data("./demo_data", num_samples=100)
    
    # 准备数据集
    print("\n准备数据集...")
    config.data.train_file = "./demo_data/train.jsonl"
    config.data.validation_file = "./demo_data/validation.jsonl"
    config.data.test_file = "./demo_data/test.jsonl"
    
    datasets = processor.prepare_datasets()
    
    # 预览数据
    processor.preview_data(datasets, num_samples=2)

def demo_training_simulation():
    """演示训练流程（模拟）"""
    print("\n" + "="*50)
    print("3. 训练流程演示（模拟）")
    print("="*50)
    
    # 创建配置
    config = Config()
    config.training.num_train_epochs = 1
    config.training.per_device_train_batch_size = 1
    config.training.save_steps = 10
    config.training.eval_steps = 10
    config.training.logging_steps = 5
    config.training.output_dir = "./demo_output"
    
    print("训练配置:")
    print(f"- 训练轮数: {config.training.num_train_epochs}")
    print(f"- 批次大小: {config.training.per_device_train_batch_size}")
    print(f"- 输出目录: {config.training.output_dir}")
    
    # 模拟训练过程
    print("\n模拟训练过程...")
    steps = 20
    for step in range(1, steps + 1):
        # 模拟损失下降
        loss = 2.0 * (1 - step / steps) + 0.5
        
        if step % 5 == 0:
            print(f"Step {step}/{steps}: Loss = {loss:.4f}")
        
        time.sleep(0.1)  # 模拟训练时间
    
    print("模拟训练完成!")
    print(f"最终损失: {loss:.4f}")

def demo_inference_simulation():
    """演示推理流程（模拟）"""
    print("\n" + "="*50)
    print("4. 推理流程演示（模拟）")
    print("="*50)
    
    # 测试文本
    test_texts = [
        "今天天气真好，适合出门散步。",
        "这个产品质量太差了，完全是垃圾！",
        "这种说法有待进一步验证。",
        "学习新技能需要持续的努力和练习。",
        "某某公司就是骗子，大家千万别相信！"
    ]
    
    # 模拟推理结果
    predictions = ["合规", "违规", "疑似", "合规", "违规"]
    confidences = [0.95, 0.88, 0.72, 0.91, 0.85]
    
    print("文本审核结果:")
    print("-" * 60)
    
    for i, (text, pred, conf) in enumerate(zip(test_texts, predictions, confidences), 1):
        print(f"文本 {i}: {text}")
        print(f"审核结果: {pred} (置信度: {conf:.2f})")
        print("-" * 60)
    
    # 统计结果
    from collections import Counter
    pred_counts = Counter(predictions)
    
    print("\n审核结果统计:")
    for label, count in pred_counts.items():
        percentage = count / len(predictions) * 100
        print(f"  {label}: {count} 条 ({percentage:.1f}%)")

def demo_evaluation():
    """演示模型评估"""
    print("\n" + "="*50)
    print("5. 模型评估演示")
    print("="*50)
    
    # 模拟评估数据
    true_labels = ["合规", "违规", "疑似", "合规", "违规", "合规", "疑似", "违规"]
    pred_labels = ["合规", "违规", "合规", "合规", "违规", "合规", "疑似", "疑似"]
    
    # 计算准确率
    correct = sum(1 for t, p in zip(true_labels, pred_labels) if t == p)
    accuracy = correct / len(true_labels)
    
    print(f"测试样本数: {len(true_labels)}")
    print(f"正确预测数: {correct}")
    print(f"准确率: {accuracy:.4f}")
    
    # 各类别统计
    from collections import Counter
    true_counts = Counter(true_labels)
    pred_counts = Counter(pred_labels)
    
    print(f"\n真实标签分布:")
    for label, count in true_counts.items():
        print(f"  {label}: {count}")
    
    print(f"\n预测标签分布:")
    for label, count in pred_counts.items():
        print(f"  {label}: {count}")
    
    # 混淆矩阵（简化版）
    print(f"\n混淆矩阵:")
    labels = ["合规", "违规", "疑似"]
    print(f"{'':>8} {'合规':>6} {'违规':>6} {'疑似':>6}")
    
    for true_label in labels:
        row = [true_label]
        for pred_label in labels:
            count = sum(1 for t, p in zip(true_labels, pred_labels) 
                       if t == true_label and p == pred_label)
            row.append(f"{count:>6}")
        print(" ".join(row))

def demo_system_info():
    """演示系统信息"""
    print("\n" + "="*50)
    print("6. 系统信息演示")
    print("="*50)
    
    # 获取GPU信息
    get_gpu_info()
    
    # 模拟内存使用情况
    print("\n内存使用情况:")
    print("- 系统内存: 32.0 GB")
    print("- 可用内存: 28.5 GB")
    print("- GPU显存: 80.0 GB")
    print("- 已用显存: 45.2 GB")
    
    # 模拟训练统计
    print("\n训练统计:")
    print("- 训练样本数: 700")
    print("- 验证样本数: 200") 
    print("- 测试样本数: 100")
    print("- 可训练参数: 33,554,432 (0.42%)")
    print("- 总参数量: 7,987,456,789")

def main():
    """主演示函数"""
    print("Qwen2.5文本审核模型微调项目演示")
    print("="*60)
    
    # 设置日志
    setup_logging()
    
    try:
        # 1. 配置管理演示
        demo_config()
        
        # 2. 数据处理演示
        demo_data_processing()
        
        # 3. 训练流程演示
        demo_training_simulation()
        
        # 4. 推理流程演示
        demo_inference_simulation()
        
        # 5. 模型评估演示
        demo_evaluation()
        
        # 6. 系统信息演示
        demo_system_info()
        
        print("\n" + "="*60)
        print("演示完成!")
        print("="*60)
        
        print("\n下一步操作:")
        print("1. 运行 python train.py --create_sample_data 创建真实数据")
        print("2. 运行 python train.py 开始训练")
        print("3. 运行 python inference.py --help 查看推理选项")
        print("4. 查看 README.md 了解详细使用方法")
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        print("这可能是因为缺少某些依赖或模型文件")
        print("请按照 README.md 中的说明完成环境安装")

if __name__ == "__main__":
    main()
