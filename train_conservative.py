#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守的训练脚本 - 只优化自身，不影响其他服务
专注于内部优化，避免影响其他正在运行的服务
"""

import os
import gc
import torch
import logging
from pathlib import Path

# 设置环境变量（只影响当前进程）
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True,max_split_size_mb:128"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["OMP_NUM_THREADS"] = "1"

# 导入配置
from config_minimal import MinimalConfig
from model_trainer import QwenModerationTrainer
from utils import setup_logging

def internal_memory_cleanup():
    """内部内存清理 - 只清理当前进程的内存"""
    # Python垃圾回收
    gc.collect()
    
    # 只清理当前进程的GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def check_available_memory():
    """检查可用GPU内存，不影响其他进程"""
    if not torch.cuda.is_available():
        print("CUDA不可用")
        return False
    
    # 清理当前进程缓存
    internal_memory_cleanup()
    
    # 检查可用内存
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    allocated = torch.cuda.memory_allocated() / 1024**3
    reserved = torch.cuda.memory_reserved() / 1024**3
    available = total_memory - reserved
    
    print(f"GPU内存状态:")
    print(f"  总内存: {total_memory:.1f} GB")
    print(f"  当前进程已分配: {allocated:.2f} GB")
    print(f"  当前进程已保留: {reserved:.2f} GB")
    print(f"  估计可用: {available:.2f} GB")
    
    # 检查是否有足够内存（需要至少20GB用于7B模型）
    if available < 20:
        print(f"\n⚠️ 警告: 可用内存可能不足 ({available:.1f} GB)")
        print("建议:")
        print("1. 使用更小的模型")
        print("2. 进一步减小批次大小和序列长度")
        print("3. 如果有不需要的服务，可以手动停止")
        
        response = input("\n是否继续尝试训练? (y/n): ")
        return response.lower() == 'y'
    
    return True

def create_ultra_minimal_config():
    """创建超级保守的配置"""
    config = MinimalConfig()
    
    # 进一步减小参数
    config.data.max_seq_length = 64  # 极小序列长度
    config.lora.r = 8  # 极小LoRA秩
    config.lora.lora_alpha = 16
    config.training.gradient_accumulation_steps = 128  # 大幅增加梯度累积
    config.training.num_train_epochs = 1  # 只训练1轮用于测试
    
    # 减少目标模块
    config.lora.target_modules = ["q_proj", "v_proj"]  # 只保留2个模块
    
    # 禁用所有可能的多进程
    config.training.dataloader_num_workers = 0
    config.data.preprocessing_num_workers = 1
    
    return config

def monitor_memory_usage():
    """监控内存使用，但不干扰其他进程"""
    class ConservativeMemoryMonitor:
        def __init__(self):
            self.initial_memory = 0
            self.peak_memory = 0
            if torch.cuda.is_available():
                torch.cuda.reset_peak_memory_stats()
                self.initial_memory = torch.cuda.memory_allocated() / 1024**3
        
        def log_memory(self, step_name):
            if torch.cuda.is_available():
                current = torch.cuda.memory_allocated() / 1024**3
                peak = torch.cuda.max_memory_allocated() / 1024**3
                self.peak_memory = max(self.peak_memory, peak)
                
                print(f"\n[{step_name}] 内存使用:")
                print(f"  当前: {current:.2f} GB")
                print(f"  峰值: {peak:.2f} GB")
                print(f"  增量: {current - self.initial_memory:.2f} GB")
                
                # 如果内存使用过高，给出警告但不强制停止
                if current > 50:
                    print(f"  ⚠️ 内存使用较高: {current:.2f} GB")
        
        def cleanup_if_needed(self):
            """在需要时进行内部清理"""
            if torch.cuda.is_available():
                current = torch.cuda.memory_allocated() / 1024**3
                if current > 40:  # 如果超过40GB，进行清理
                    print("执行内部内存清理...")
                    internal_memory_cleanup()
    
    return ConservativeMemoryMonitor()

def main():
    """主函数 - 保守训练"""
    print("保守训练脚本 - 不影响其他服务")
    print("="*50)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 1. 检查可用内存
        if not check_available_memory():
            print("内存检查未通过，退出训练")
            return
        
        # 2. 创建超级保守的配置
        config = create_ultra_minimal_config()
        
        print("\n超级保守配置:")
        print(f"  序列长度: {config.data.max_seq_length}")
        print(f"  LoRA秩: {config.lora.r}")
        print(f"  目标模块: {config.lora.target_modules}")
        print(f"  批次大小: {config.training.per_device_train_batch_size}")
        print(f"  梯度累积: {config.training.gradient_accumulation_steps}")
        print(f"  有效批次: {config.training.per_device_train_batch_size * config.training.gradient_accumulation_steps}")
        
        # 3. 创建内存监控
        memory_monitor = monitor_memory_usage()
        
        # 4. 分步执行训练，每步监控
        logger.info("开始保守训练流程...")
        
        # 4.1 创建训练器
        memory_monitor.log_memory("训练器创建前")
        trainer = QwenModerationTrainer(config)
        memory_monitor.log_memory("训练器创建后")
        
        # 4.2 加载模型
        logger.info("加载模型...")
        trainer.load_model_and_tokenizer()
        memory_monitor.log_memory("模型加载后")
        memory_monitor.cleanup_if_needed()
        
        # 4.3 设置LoRA
        logger.info("设置LoRA...")
        trainer.setup_lora()
        memory_monitor.log_memory("LoRA设置后")
        memory_monitor.cleanup_if_needed()
        
        # 4.4 准备数据
        logger.info("准备数据...")
        trainer.prepare_data()
        memory_monitor.log_memory("数据准备后")
        memory_monitor.cleanup_if_needed()
        
        # 4.5 设置训练器
        logger.info("设置训练器...")
        trainer.setup_trainer()
        memory_monitor.log_memory("训练器设置后")
        memory_monitor.cleanup_if_needed()
        
        # 4.6 开始训练
        logger.info("开始训练...")
        train_result = trainer.train()
        memory_monitor.log_memory("训练完成后")
        
        # 4.7 保存模型
        trainer.save_model_and_config()
        memory_monitor.log_memory("模型保存后")
        
        print(f"\n✅ 训练成功完成!")
        print(f"峰值内存使用: {memory_monitor.peak_memory:.2f} GB")
        print(f"模型保存位置: {config.training.output_dir}")
        
        # 给出后续建议
        print(f"\n📝 后续建议:")
        if memory_monitor.peak_memory < 30:
            print("- 内存使用良好，可以尝试增大参数")
            print("- 可以增加序列长度到128或256")
            print("- 可以增加LoRA秩到16或32")
        else:
            print("- 内存使用接近极限，建议保持当前参数")
            print("- 如需更好效果，考虑使用更小的模型")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"GPU内存不足: {e}")
        print(f"\n❌ GPU内存不足: {e}")
        print("\n🔧 建议进一步优化:")
        print("1. 序列长度减小到32")
        print("2. LoRA秩减小到4")
        print("3. 只使用1个目标模块")
        print("4. 考虑使用更小的模型（如1.5B版本）")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        print(f"\n❌ 训练失败: {e}")
        
    finally:
        # 最后清理当前进程的内存
        print("\n🧹 清理当前进程内存...")
        internal_memory_cleanup()
        print("清理完成")

if __name__ == "__main__":
    main()
