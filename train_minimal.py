#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小内存使用的训练脚本
专门解决70GB内存占用问题
"""

import os
import gc
import torch
import logging
from pathlib import Path

# 设置最激进的内存优化环境变量
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True,max_split_size_mb:128"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
os.environ["OMP_NUM_THREADS"] = "1"  # 限制OpenMP线程

# 导入配置
from config_minimal import MinimalConfig
from model_trainer import QwenModerationTrainer, clear_gpu_memory, print_gpu_memory_usage
from utils import setup_logging

def aggressive_memory_cleanup():
    """激进的内存清理"""
    # Python垃圾回收
    for _ in range(3):
        gc.collect()
    
    # GPU内存清理
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        torch.cuda.reset_peak_memory_stats()
        torch.cuda.reset_accumulated_memory_stats()

def check_memory_before_start():
    """启动前检查内存"""
    print("启动前内存检查:")
    print_gpu_memory_usage()
    
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        allocated = torch.cuda.memory_allocated() / 1024**3
        
        if allocated > 1.0:  # 如果已经占用超过1GB
            print(f"警告: GPU已占用 {allocated:.2f} GB 内存")
            print("建议先运行 clear_gpu.py 清理GPU进程")
            
            response = input("是否继续? (y/n): ")
            if response.lower() != 'y':
                print("训练取消")
                return False
    
    return True

def monitor_memory_during_training():
    """训练过程中的内存监控"""
    class MemoryMonitor:
        def __init__(self):
            self.max_memory = 0
            
        def update(self):
            if torch.cuda.is_available():
                current = torch.cuda.memory_allocated() / 1024**3
                self.max_memory = max(self.max_memory, current)
                
                if current > 70:  # 如果超过70GB
                    print(f"警告: GPU内存使用过高 {current:.2f} GB")
                    aggressive_memory_cleanup()
    
    return MemoryMonitor()

def main():
    """主函数"""
    print("最小内存使用训练脚本")
    print("="*50)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 1. 启动前检查
        if not check_memory_before_start():
            return
        
        # 2. 激进内存清理
        aggressive_memory_cleanup()
        
        # 3. 创建最小配置
        config = MinimalConfig()
        config.print_config()
        
        # 4. 内存监控
        memory_monitor = monitor_memory_during_training()
        
        # 5. 创建训练器
        logger.info("创建训练器...")
        print_gpu_memory_usage()
        
        trainer = QwenModerationTrainer(config)
        memory_monitor.update()
        
        # 6. 分步执行训练流程，每步监控内存
        
        # 6.1 加载模型
        logger.info("加载模型...")
        trainer.load_model_and_tokenizer()
        memory_monitor.update()
        print(f"模型加载后最大内存: {memory_monitor.max_memory:.2f} GB")
        
        # 6.2 设置LoRA
        logger.info("设置LoRA...")
        trainer.setup_lora()
        memory_monitor.update()
        print(f"LoRA设置后最大内存: {memory_monitor.max_memory:.2f} GB")
        
        # 6.3 准备数据
        logger.info("准备数据...")
        trainer.prepare_data()
        memory_monitor.update()
        print(f"数据准备后最大内存: {memory_monitor.max_memory:.2f} GB")
        
        # 6.4 设置训练器
        logger.info("设置训练器...")
        trainer.setup_trainer()
        memory_monitor.update()
        print(f"训练器设置后最大内存: {memory_monitor.max_memory:.2f} GB")
        
        # 检查内存使用是否合理
        if memory_monitor.max_memory > 50:
            print(f"警告: 内存使用过高 {memory_monitor.max_memory:.2f} GB")
            print("建议:")
            print("1. 进一步减小序列长度")
            print("2. 减小LoRA秩")
            print("3. 检查是否有其他进程占用GPU")
            
            response = input("是否继续训练? (y/n): ")
            if response.lower() != 'y':
                print("训练取消")
                return
        
        # 6.5 开始训练
        logger.info("开始训练...")
        train_result = trainer.train()
        
        # 6.6 评估
        eval_result = trainer.evaluate()
        
        # 6.7 保存模型
        trainer.save_model_and_config()
        
        # 6.8 打印总结
        trainer.print_training_summary(train_result, eval_result)
        
        print(f"\n训练完成!")
        print(f"最大内存使用: {memory_monitor.max_memory:.2f} GB")
        print(f"模型保存位置: {config.training.output_dir}")
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"GPU内存不足: {e}")
        print(f"\nGPU内存不足: {e}")
        print("\n紧急建议:")
        print("1. 序列长度减小到64")
        print("2. LoRA秩减小到8")
        print("3. 使用梯度检查点")
        print("4. 考虑使用更小的模型")
        
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        print("\n训练被中断")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        print(f"\n训练失败: {e}")
        raise
        
    finally:
        # 最终清理
        aggressive_memory_cleanup()
        print("\n内存清理完成")

if __name__ == "__main__":
    main()
