# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型训练器
基于LoRA微调的训练实现
"""

import os
import sys
import json
import torch
import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from datetime import datetime
import wandb

# Transformers相关导入
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    EarlyStoppingCallback,
    TrainerCallback,
    TrainerState,
    TrainerControl
)

# PEFT相关导入
from peft import (
    LoraConfig,
    get_peft_model,
    TaskType,
    prepare_model_for_kbit_training
)

# 本地模块导入
from config import Config
from data_processor import TextModerationDataProcessor
from utils import setup_logging, get_gpu_info, save_training_log

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

class CustomTrainerCallback(TrainerCallback):
    """自定义训练回调，用于监控训练过程"""
    
    def __init__(self, config: Config):
        self.config = config
        self.training_logs = []
    
    def on_log(self, args, state, control, model=None, logs=None, **kwargs):
        """记录训练日志"""
        if logs:
            # 添加时间戳
            logs["timestamp"] = datetime.now().isoformat()
            logs["step"] = state.global_step
            logs["epoch"] = state.epoch
            
            self.training_logs.append(logs.copy())
            
            # 打印关键指标
            if "loss" in logs:
                logger.info(f"Step {state.global_step}: Loss = {logs['loss']:.4f}")
            
            if "eval_loss" in logs:
                logger.info(f"Step {state.global_step}: Eval Loss = {logs['eval_loss']:.4f}")
    
    def on_train_end(self, args, state, control, model=None, **kwargs):
        """训练结束时保存日志"""
        log_file = Path(args.output_dir) / "training_logs.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.training_logs, f, indent=2, ensure_ascii=False)
        logger.info(f"训练日志已保存到: {log_file}")

def clear_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def print_gpu_memory_usage():
    """打印GPU内存使用情况"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        reserved = torch.cuda.memory_reserved() / 1024**3   # GB
        max_allocated = torch.cuda.max_memory_allocated() / 1024**3  # GB

        logger.info(f"GPU内存使用情况:")
        logger.info(f"  已分配: {allocated:.2f} GB")
        logger.info(f"  已保留: {reserved:.2f} GB")
        logger.info(f"  峰值分配: {max_allocated:.2f} GB")

class QwenModerationTrainer:
    """Qwen2.5文本审核模型训练器"""

    def __init__(self, config: Config):
        """
        初始化训练器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.tokenizer = None
        self.model = None
        self.datasets = None
        self.trainer = None
        
        # 设置环境变量
        os.environ["CUDA_VISIBLE_DEVICES"] = config.system.cuda_visible_devices
        os.environ["TOKENIZERS_PARALLELISM"] = "false"
        # 设置PyTorch CUDA内存分配策略
        os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"

        # 创建输出目录
        Path(config.training.output_dir).mkdir(parents=True, exist_ok=True)

        # 清理GPU缓存
        clear_gpu_memory()
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()

        # 打印GPU信息
        get_gpu_info()
        print_gpu_memory_usage()
    
    def load_model_and_tokenizer(self):
        """加载模型和分词器
        注意：使用镜像站配置需要通过环境变量设置：
        export HF_ENDPOINT="https://modelscope.cn/api/v1/models"
        export MODELSCOPE_MIRROR=1
        """
        logger.info(f"加载基础模型: {self.config.model.model_name_or_path}")
        
        # 解决缓存目录名称中的小数点被替换为下划线的问题
        original_model_path = self.config.model.model_name_or_path
        cache_dir = self.config.model.cache_dir
        
        # 尝试拆分模型路径，处理可能的路径替换
        if "/" in original_model_path:
            # 如果是HF路径，如Qwen/Qwen2.5-7B-Instruct
            org, model_name = original_model_path.split("/", 1)
            # 寻找可能的缓存目录名称，如Qwen2___5-7B-Instruct
            possible_model_paths = [
                original_model_path,  # 原始路径
                f"{cache_dir}/{org.lower()}/{model_name}",  # 原始缓存路径
                f"{cache_dir}/{org.lower()}/{model_name.replace('.', '___')}",  # 点替换为下划线
            ]
            logger.info(f"将尝试以下路径: {possible_model_paths}")
        else:
            possible_model_paths = [original_model_path]
        
        # 加载分词器的多种尝试
        tokenizer_loaded = False
        for model_path in possible_model_paths:
            try:
                logger.info(f"尝试从路径加载分词器: {model_path}")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    model_path,
                    trust_remote_code=self.config.model.trust_remote_code,
                    cache_dir=self.config.model.cache_dir,
                    local_files_only=True
                )
                logger.info(f"从 {model_path} 加载分词器成功")
                tokenizer_loaded = True
                break
            except Exception as e:
                logger.warning(f"从 {model_path} 加载分词器失败: {e}")
        
        # 如果所有尝试都失败，则尝试从网络加载
        if not tokenizer_loaded:
            try:
                logger.info("尝试从网络加载分词器...")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    original_model_path,
                    trust_remote_code=self.config.model.trust_remote_code,
                    cache_dir=self.config.model.cache_dir
                )
                logger.info("从网络加载分词器成功")
            except Exception as e:
                logger.error(f"无法加载分词器: {e}")
                raise
        
        # 确保 tokenizer 有 pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        # 设置模型精度
        torch_dtype = {
            "float16": torch.float16,
            "bfloat16": torch.bfloat16,
            "float32": torch.float32
        }.get(self.config.model.torch_dtype, torch.bfloat16)

        logger.info(f"使用{self.config.model.torch_dtype}精度加载模型")

        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 加载模型的多种尝试
        model_loaded = False
        for model_path in possible_model_paths:
            try:
                logger.info(f"尝试从路径加载模型: {model_path}")
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    torch_dtype=torch_dtype,
                    device_map=self.config.model.device_map,
                    trust_remote_code=self.config.model.trust_remote_code,
                    attn_implementation=self.config.model.attn_implementation,
                    cache_dir=self.config.model.cache_dir,
                    local_files_only=True,
                    low_cpu_mem_usage=True,  # 降低CPU内存使用
                    use_cache=False  # 训练时关闭缓存
                )
                logger.info(f"从 {model_path} 加载模型成功")
                model_loaded = True
                break
            except Exception as e:
                logger.warning(f"从 {model_path} 加载模型失败: {e}")
        
        # 如果所有尝试都失败，则尝试从网络加载
        if not model_loaded:
            try:
                logger.info("尝试从网络加载模型...")
                self.model = AutoModelForCausalLM.from_pretrained(
                    original_model_path,
                    torch_dtype=torch_dtype,
                    device_map=self.config.model.device_map,
                    trust_remote_code=self.config.model.trust_remote_code,
                    attn_implementation=self.config.model.attn_implementation,
                    cache_dir=self.config.model.cache_dir,
                    low_cpu_mem_usage=True,  # 降低CPU内存使用
                    use_cache=False  # 训练时关闭缓存
                )
                logger.info("从网络加载模型成功")
            except Exception as e:
                logger.error(f"无法加载模型: {e}")
                raise
        
        # 准备模型用于训练
        self.model = prepare_model_for_kbit_training(self.model)

        # 清理内存
        clear_gpu_memory()

        logger.info(f"基础模型加载完成")
        logger.info(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        print_gpu_memory_usage()

        return self.model
    
    def setup_lora(self):
        """设置LoRA配置"""
        logger.info("设置LoRA配置...")
        
        # 创建LoRA配置
        lora_config = LoraConfig(
            r=self.config.lora.r,
            lora_alpha=self.config.lora.lora_alpha,
            target_modules=self.config.lora.target_modules,
            lora_dropout=self.config.lora.lora_dropout,
            bias=self.config.lora.bias,
            task_type=TaskType.CAUSAL_LM,
            fan_in_fan_out=self.config.lora.fan_in_fan_out,
            init_lora_weights=self.config.lora.init_lora_weights
        )
        
        # 应用LoRA到模型
        self.model = get_peft_model(self.model, lora_config)

        # 清理内存
        clear_gpu_memory()

        # 打印可训练参数信息
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in self.model.parameters())

        logger.info(f"LoRA配置完成")
        logger.info(f"可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.2f}%)")
        logger.info(f"总参数量: {total_params:,}")
        print_gpu_memory_usage()
        
        # 打印LoRA配置详情
        print("\n" + "="*50)
        print("LoRA配置详情")
        print("="*50)
        print(f"LoRA秩 (r): {self.config.lora.r}")
        print(f"LoRA alpha: {self.config.lora.lora_alpha}")
        print(f"LoRA dropout: {self.config.lora.lora_dropout}")
        print(f"目标模块: {', '.join(self.config.lora.target_modules)}")
        print(f"可训练参数比例: {trainable_params/total_params*100:.2f}%")
        print("="*50)
        
        return self.model
    
    def prepare_data(self):
        """准备训练数据"""
        logger.info("准备训练数据...")

        # 清理内存
        clear_gpu_memory()

        # 创建数据处理器
        data_processor = TextModerationDataProcessor(self.tokenizer, self.config)

        # 准备数据集
        self.datasets = data_processor.prepare_datasets()

        # 清理数据处理器引用，释放内存
        del data_processor
        clear_gpu_memory()

        # 预览数据（减少样本数）
        print(f"\n数据集大小:")
        for split_name, dataset in self.datasets.items():
            print(f"  {split_name}: {len(dataset)} 条样本")

        logger.info("训练数据准备完成")
        print_gpu_memory_usage()
        return self.datasets
    
    def setup_training_arguments(self) -> TrainingArguments:
        """设置训练参数"""
        logger.info("设置训练参数...")
        
        training_args = TrainingArguments(
            # 基础配置
            output_dir=self.config.training.output_dir,
            run_name=self.config.training.run_name,
            
            # 训练配置
            num_train_epochs=self.config.training.num_train_epochs,
            per_device_train_batch_size=self.config.training.per_device_train_batch_size,
            per_device_eval_batch_size=self.config.training.per_device_eval_batch_size,
            gradient_accumulation_steps=self.config.training.gradient_accumulation_steps,
            
            # 学习率配置
            learning_rate=self.config.training.learning_rate,
            weight_decay=self.config.training.weight_decay,
            lr_scheduler_type=self.config.training.lr_scheduler_type,
            warmup_ratio=self.config.training.warmup_ratio,
            
            # 优化器配置
            optim=self.config.training.optim,
            adam_beta1=self.config.training.adam_beta1,
            adam_beta2=self.config.training.adam_beta2,
            adam_epsilon=self.config.training.adam_epsilon,
            max_grad_norm=self.config.training.max_grad_norm,
            
            # 保存配置
            save_strategy=self.config.training.save_strategy,
            save_steps=self.config.training.save_steps,
            save_total_limit=self.config.training.save_total_limit,
            
            # 日志配置
            logging_strategy=self.config.training.logging_strategy,
            logging_steps=self.config.training.logging_steps,
            report_to=self.config.training.report_to,
            
            # 评估配置
            eval_strategy=self.config.training.evaluation_strategy,
            eval_steps=self.config.training.eval_steps,
            load_best_model_at_end=self.config.training.load_best_model_at_end,
            metric_for_best_model=self.config.training.metric_for_best_model,
            greater_is_better=self.config.training.greater_is_better,
            
            # 性能优化
            dataloader_num_workers=self.config.training.dataloader_num_workers,
            remove_unused_columns=self.config.training.remove_unused_columns,
            group_by_length=self.config.training.group_by_length,
            ddp_find_unused_parameters=self.config.training.ddp_find_unused_parameters,
            
            # 混合精度
            fp16=self.config.training.fp16,
            bf16=self.config.training.bf16,
            tf32=self.config.training.tf32,
            
            # 其他配置
            seed=self.config.training.seed,
        )
        
        logger.info("训练参数设置完成")
        return training_args
    
    def setup_trainer(self) -> Trainer:
        """设置训练器"""
        logger.info("设置训练器...")
        
        # 获取训练参数
        training_args = self.setup_training_arguments()
        
        # 数据整理器
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,  # 因果语言模型不使用MLM
            pad_to_multiple_of=8,  # 提升性能
        )
        
        # 创建回调
        callbacks = [
            CustomTrainerCallback(self.config),
            EarlyStoppingCallback(early_stopping_patience=3)  # 早停
        ]
        
        # 创建训练器
        self.trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=self.datasets["train"],
            eval_dataset=self.datasets["validation"] if "validation" in self.datasets else None,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
            callbacks=callbacks,
        )
        
        logger.info("训练器设置完成")
        return self.trainer
    
    def train(self):
        """开始训练"""
        logger.info("开始训练...")
        
        # 打印训练信息
        print("\n" + "="*50)
        print("训练信息")
        print("="*50)
        print(f"模型: {self.config.model.model_name_or_path}")
        print(f"训练样本数: {len(self.datasets['train'])}")
        if "validation" in self.datasets:
            print(f"验证样本数: {len(self.datasets['validation'])}")
        print(f"训练轮数: {self.config.training.num_train_epochs}")
        print(f"批次大小: {self.config.training.per_device_train_batch_size}")
        print(f"梯度累积步数: {self.config.training.gradient_accumulation_steps}")
        print(f"有效批次大小: {self.config.training.per_device_train_batch_size * self.config.training.gradient_accumulation_steps}")
        print(f"学习率: {self.config.training.learning_rate}")
        print(f"输出目录: {self.config.training.output_dir}")
        print("="*50)
        
        # 开始训练
        print_gpu_memory_usage()
        train_result = self.trainer.train()

        # 清理内存
        clear_gpu_memory()

        # 保存训练结果
        self.trainer.save_model()
        self.trainer.save_state()
        
        # 保存训练指标
        metrics = train_result.metrics
        metrics["train_samples"] = len(self.datasets["train"])
        
        self.trainer.log_metrics("train", metrics)
        self.trainer.save_metrics("train", metrics)
        
        logger.info("训练完成！")
        return train_result
    
    def evaluate(self):
        """评估模型"""
        if "validation" in self.datasets:
            logger.info("开始评估...")
            
            eval_result = self.trainer.evaluate()
            
            self.trainer.log_metrics("eval", eval_result)
            self.trainer.save_metrics("eval", eval_result)
            
            logger.info(f"评估完成，验证损失: {eval_result['eval_loss']:.4f}")
            return eval_result
        else:
            logger.warning("没有验证集，跳过评估")
            return None
    
    def save_model_and_config(self):
        """保存模型和配置"""
        logger.info("保存模型和配置...")
        
        # 保存模型
        output_dir = Path(self.config.training.output_dir)
        
        # 保存LoRA权重
        self.model.save_pretrained(output_dir / "lora_weights")
        
        # 保存分词器
        self.tokenizer.save_pretrained(output_dir / "tokenizer")
        
        # 保存配置
        self.config.save_config(str(output_dir / "training_config.json"))
        
        # 保存模型信息
        model_info = {
            "base_model": self.config.model.model_name_or_path,
            "lora_config": self.config.lora.__dict__,
            "training_config": self.config.training.__dict__,
            "model_size": sum(p.numel() for p in self.model.parameters()),
            "trainable_params": sum(p.numel() for p in self.model.parameters() if p.requires_grad),
            "save_time": datetime.now().isoformat()
        }
        
        with open(output_dir / "model_info.json", 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"模型和配置已保存到: {output_dir}")
    
    def run_full_training(self):
        """运行完整的训练流程"""
        try:
            # 1. 加载模型和分词器
            self.load_model_and_tokenizer()
            
            # 2. 设置LoRA
            self.setup_lora()
            
            # 3. 准备数据
            self.prepare_data()
            
            # 4. 设置训练器
            self.setup_trainer()
            
            # 5. 开始训练
            train_result = self.train()
            
            # 6. 评估模型
            eval_result = self.evaluate()
            
            # 7. 保存模型和配置
            self.save_model_and_config()
            
            # 8. 打印总结
            self.print_training_summary(train_result, eval_result)
            
            logger.info("完整训练流程执行完成！")
            
        except Exception as e:
            logger.error(f"训练过程中出现错误: {e}")
            raise
    
    def print_training_summary(self, train_result, eval_result):
        """打印训练总结"""
        print("\n" + "="*50)
        print("训练总结")
        print("="*50)
        
        print(f"训练损失: {train_result.metrics.get('train_loss', 'N/A'):.4f}")
        print(f"训练步数: {train_result.metrics.get('train_steps_per_second', 'N/A'):.2f} steps/sec")
        
        if eval_result:
            print(f"验证损失: {eval_result.get('eval_loss', 'N/A'):.4f}")
        
        print(f"输出目录: {self.config.training.output_dir}")
        print(f"模型保存位置: {self.config.training.output_dir}/lora_weights")
        
        print("\n使用方法:")
        print("1. 加载微调后的模型进行推理")
        print("2. 查看训练日志和指标")
        print("3. 使用验证集进一步测试模型性能")
        
        print("="*50)

def main():
    """主函数"""
    # 加载配置
    config = Config()
    
    # 打印配置
    config.print_config()
    
    # 创建训练器
    trainer = QwenModerationTrainer(config)
    
    # 运行完整训练
    trainer.run_full_training()

if __name__ == "__main__":
    main()
