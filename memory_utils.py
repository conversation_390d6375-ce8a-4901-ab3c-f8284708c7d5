# -*- coding: utf-8 -*-
"""
内存管理工具
解决GPU内存不足问题的核心工具
"""

import os
import gc
import torch
import subprocess
import psutil

def setup_memory_optimization():
    """设置内存优化环境变量（只影响当前进程）"""
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True,max_split_size_mb:128"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["OMP_NUM_THREADS"] = "1"

def clear_current_process_memory():
    """清理当前进程的内存（不影响其他进程）"""
    # Python垃圾回收
    gc.collect()
    
    # 只清理当前进程的GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def print_memory_status(step_name=""):
    """打印当前内存状态"""
    if step_name:
        print(f"\n[{step_name}] 内存状态:")
    else:
        print("\n内存状态:")
    
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        print(f"  GPU总内存: {total:.1f} GB")
        print(f"  已分配: {allocated:.2f} GB")
        print(f"  已保留: {reserved:.2f} GB")
        print(f"  可用: {total - reserved:.2f} GB")
    else:
        print("  CUDA不可用")

def check_memory_availability():
    """检查内存可用性，给出建议"""
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    # 清理当前进程缓存
    clear_current_process_memory()
    
    total = torch.cuda.get_device_properties(0).total_memory / 1024**3
    reserved = torch.cuda.memory_reserved() / 1024**3
    available = total - reserved
    
    print(f"GPU内存评估:")
    print(f"  总内存: {total:.1f} GB")
    print(f"  估计可用: {available:.1f} GB")
    
    if available >= 25:
        print("✅ 内存充足，可以使用标准配置")
        return True
    elif available >= 15:
        print("🟡 内存中等，建议使用优化配置")
        return True
    elif available >= 8:
        print("🟠 内存紧张，需要使用最小配置")
        return True
    else:
        print("❌ 内存不足，建议等待其他任务完成")
        return False

def get_optimized_config():
    """根据可用内存返回优化配置"""
    if not torch.cuda.is_available():
        return None
    
    clear_current_process_memory()
    total = torch.cuda.get_device_properties(0).total_memory / 1024**3
    reserved = torch.cuda.memory_reserved() / 1024**3
    available = total - reserved
    
    if available >= 25:
        # 标准配置
        return {
            'max_seq_length': 512,
            'lora_r': 64,
            'lora_alpha': 128,
            'batch_size': 2,
            'gradient_accumulation_steps': 8,
            'target_modules': ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        }
    elif available >= 15:
        # 优化配置
        return {
            'max_seq_length': 256,
            'lora_r': 32,
            'lora_alpha': 64,
            'batch_size': 1,
            'gradient_accumulation_steps': 16,
            'target_modules': ["q_proj", "v_proj", "o_proj", "gate_proj"]
        }
    else:
        # 最小配置
        return {
            'max_seq_length': 128,
            'lora_r': 16,
            'lora_alpha': 32,
            'batch_size': 1,
            'gradient_accumulation_steps': 32,
            'target_modules': ["q_proj", "v_proj"]
        }

def show_gpu_processes():
    """显示GPU进程信息（只查看，不操作）"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-compute-apps=pid,process_name,used_memory', 
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            print("\n当前GPU进程:")
            print(f"{'PID':<8} {'进程名':<20} {'内存(MB)':<10}")
            print("-" * 40)
            
            lines = result.stdout.strip().split('\n')
            total_memory = 0
            
            for line in lines:
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        pid = int(parts[0])
                        process_name = parts[1]
                        memory_mb = int(parts[2])
                        total_memory += memory_mb
                        
                        print(f"{pid:<8} {process_name:<20} {memory_mb:<10}")
            
            print("-" * 40)
            print(f"总GPU内存使用: {total_memory} MB ({total_memory/1024:.1f} GB)")
        else:
            print("未发现GPU进程")
            
    except Exception as e:
        print(f"检查GPU进程失败: {e}")

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self):
        self.initial_memory = 0
        self.peak_memory = 0
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
            self.initial_memory = torch.cuda.memory_allocated() / 1024**3
    
    def log_memory(self, step_name):
        """记录内存使用"""
        if torch.cuda.is_available():
            current = torch.cuda.memory_allocated() / 1024**3
            peak = torch.cuda.max_memory_allocated() / 1024**3
            self.peak_memory = max(self.peak_memory, peak)
            
            print(f"\n[{step_name}]")
            print(f"  当前内存: {current:.2f} GB")
            print(f"  峰值内存: {peak:.2f} GB")
            print(f"  增量: {current - self.initial_memory:.2f} GB")
            
            # 内存使用警告
            if current > 50:
                print(f"  ⚠️ 内存使用较高")
                clear_current_process_memory()
    
    def get_peak_memory(self):
        """获取峰值内存使用"""
        return self.peak_memory

def main():
    """测试内存工具"""
    print("GPU内存管理工具测试")
    print("="*40)
    
    # 设置优化
    setup_memory_optimization()
    
    # 检查内存
    check_memory_availability()
    
    # 显示进程
    show_gpu_processes()
    
    # 获取建议配置
    config = get_optimized_config()
    if config:
        print(f"\n建议配置:")
        for key, value in config.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
