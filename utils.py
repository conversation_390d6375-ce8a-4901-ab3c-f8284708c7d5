# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型微调工具函数
包含日志设置、GPU监控、模型推理等实用功能
"""

import os
import sys
import json
import logging
import subprocess
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from datetime import datetime
import psutil
import GPUtil
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import matplotlib.pyplot as plt
import seaborn as sns

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
    """
    # 创建日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("datasets").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)

def get_gpu_info():
    """获取GPU信息"""
    print("\n" + "="*50)
    print("GPU信息")
    print("="*50)
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"PyTorch版本: {torch.__version__}")
        print(f"可用GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu = torch.cuda.get_device_properties(i)
            print(f"\nGPU {i}: {gpu.name}")
            print(f"  显存: {gpu.total_memory / 1024**3:.1f} GB")
            print(f"  计算能力: {gpu.major}.{gpu.minor}")
            
            # 获取当前显存使用情况
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
                print(f"  已分配显存: {memory_allocated:.1f} GB")
                print(f"  已预留显存: {memory_reserved:.1f} GB")
    else:
        print("CUDA不可用，将使用CPU训练")
    
    print("="*50)

def get_system_info():
    """获取系统信息"""
    print("\n" + "="*50)
    print("系统信息")
    print("="*50)
    
    # CPU信息
    print(f"CPU核心数: {psutil.cpu_count(logical=False)} 物理核心")
    print(f"CPU线程数: {psutil.cpu_count(logical=True)} 逻辑核心")
    print(f"CPU使用率: {psutil.cpu_percent(interval=1):.1f}%")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / 1024**3:.1f} GB")
    print(f"可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # 磁盘信息
    disk = psutil.disk_usage('/')
    print(f"磁盘总容量: {disk.total / 1024**3:.1f} GB")
    print(f"磁盘可用空间: {disk.free / 1024**3:.1f} GB")
    print(f"磁盘使用率: {(disk.used / disk.total) * 100:.1f}%")
    
    print("="*50)

def monitor_gpu_memory():
    """监控GPU内存使用情况"""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3
            
            print(f"GPU {i} 内存使用: {allocated:.1f}GB / {total:.1f}GB ({allocated/total*100:.1f}%)")

def clear_gpu_cache():
    """清理GPU缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("GPU缓存已清理")

def save_training_log(log_data: Dict[str, Any], output_file: str):
    """
    保存训练日志
    
    Args:
        log_data: 日志数据
        output_file: 输出文件路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(log_data, f, indent=2, ensure_ascii=False)
    print(f"训练日志已保存到: {output_file}")

def plot_training_curves(log_file: str, output_dir: str = "./plots"):
    """
    绘制训练曲线
    
    Args:
        log_file: 训练日志文件
        output_dir: 输出目录
    """
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 加载日志数据
    with open(log_file, 'r', encoding='utf-8') as f:
        logs = json.load(f)
    
    # 提取训练和验证损失
    train_steps = []
    train_losses = []
    eval_steps = []
    eval_losses = []
    
    for log in logs:
        if 'loss' in log:
            train_steps.append(log['step'])
            train_losses.append(log['loss'])
        
        if 'eval_loss' in log:
            eval_steps.append(log['step'])
            eval_losses.append(log['eval_loss'])
    
    # 绘制损失曲线
    plt.figure(figsize=(12, 6))
    
    # 训练损失
    plt.subplot(1, 2, 1)
    plt.plot(train_steps, train_losses, 'b-', label='训练损失', linewidth=2)
    plt.xlabel('训练步数')
    plt.ylabel('损失值')
    plt.title('训练损失曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 验证损失
    if eval_losses:
        plt.subplot(1, 2, 2)
        plt.plot(eval_steps, eval_losses, 'r-', label='验证损失', linewidth=2)
        plt.xlabel('训练步数')
        plt.ylabel('损失值')
        plt.title('验证损失曲线')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(Path(output_dir) / 'training_curves.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"训练曲线已保存到: {output_dir}/training_curves.png")

class ModelInference:
    """模型推理类"""
    
    def __init__(self, base_model_path: str, lora_weights_path: str, tokenizer_path: Optional[str] = None):
        """
        初始化推理器
        
        Args:
            base_model_path: 基础模型路径
            lora_weights_path: LoRA权重路径
            tokenizer_path: 分词器路径
        """
        self.base_model_path = base_model_path
        self.lora_weights_path = lora_weights_path
        self.tokenizer_path = tokenizer_path or base_model_path
        
        self.tokenizer = None
        self.model = None
        
        self.load_model()
    
    def load_model(self):
        """加载模型和分词器"""
        print("加载推理模型...")
        
        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.tokenizer_path,
            trust_remote_code=True
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 加载基础模型
        base_model = AutoModelForCausalLM.from_pretrained(
            self.base_model_path,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # 加载LoRA权重
        self.model = PeftModel.from_pretrained(
            base_model,
            self.lora_weights_path,
            torch_dtype=torch.bfloat16
        )
        
        # 合并权重以提升推理速度
        self.model = self.model.merge_and_unload()
        
        print("推理模型加载完成")
    
    def format_input(self, text: str) -> str:
        """格式化输入文本"""
        system_prompt = """你是一个专业的文本内容审核助手。请对给定的文本内容进行审核，判断其是否符合平台规范。

审核标准：
- 合规：内容健康正面，符合平台规范
- 违规：内容明显违反平台规范，包含有害信息
- 疑似：内容存在争议或需要进一步人工审核

请仅回答：合规、违规、疑似 中的一个。"""
        
        user_input = f"请审核以下文本内容：\n{text}"
        
        conversation = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
        
        return conversation
    
    def predict(self, text: str, max_new_tokens: int = 10, temperature: float = 0.1) -> str:
        """
        预测文本审核结果
        
        Args:
            text: 待审核文本
            max_new_tokens: 最大生成token数
            temperature: 生成温度
            
        Returns:
            审核结果
        """
        # 格式化输入
        formatted_input = self.format_input(text)
        
        # 分词
        inputs = self.tokenizer(
            formatted_input,
            return_tensors="pt",
            truncation=True,
            max_length=2048
        )
        
        # 移动到GPU
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}
        
        # 生成
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
        
        # 解码输出
        generated_text = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:],
            skip_special_tokens=True
        ).strip()
        
        return generated_text
    
    def batch_predict(self, texts: List[str], batch_size: int = 8) -> List[str]:
        """
        批量预测
        
        Args:
            texts: 文本列表
            batch_size: 批次大小
            
        Returns:
            预测结果列表
        """
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            batch_results = []
            
            for text in batch_texts:
                result = self.predict(text)
                batch_results.append(result)
            
            results.extend(batch_results)
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        return results

def evaluate_model_performance(predictions: List[str], ground_truth: List[str]) -> Dict[str, float]:
    """
    评估模型性能
    
    Args:
        predictions: 预测结果
        ground_truth: 真实标签
        
    Returns:
        评估指标
    """
    from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
    
    # 计算准确率
    accuracy = accuracy_score(ground_truth, predictions)
    
    # 计算精确率、召回率、F1分数
    precision, recall, f1, support = precision_recall_fscore_support(
        ground_truth, predictions, average='weighted'
    )
    
    # 计算混淆矩阵
    cm = confusion_matrix(ground_truth, predictions)
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': cm.tolist()
    }
    
    return metrics

def create_demo_script():
    """创建演示脚本"""
    demo_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen2.5文本审核模型演示脚本
"""

from utils import ModelInference

def main():
    # 模型路径（请根据实际情况修改）
    base_model_path = "Qwen/Qwen2.5-7B-Instruct"
    lora_weights_path = "./output/lora_weights"
    
    # 创建推理器
    inference = ModelInference(base_model_path, lora_weights_path)
    
    # 测试文本
    test_texts = [
        "今天天气真好，适合出门散步。",
        "这个产品质量太差了，完全是垃圾！",
        "这种说法有待进一步验证。"
    ]
    
    print("文本审核演示")
    print("="*50)
    
    for i, text in enumerate(test_texts, 1):
        result = inference.predict(text)
        print(f"文本 {i}: {text}")
        print(f"审核结果: {result}")
        print("-" * 30)

if __name__ == "__main__":
    main()
'''
    
    with open("demo.py", 'w', encoding='utf-8') as f:
        f.write(demo_script)
    
    print("演示脚本已创建: demo.py")

def main():
    """测试工具函数"""
    print("测试工具函数...")
    
    # 获取系统信息
    get_system_info()
    
    # 获取GPU信息
    get_gpu_info()
    
    # 创建演示脚本
    create_demo_script()

if __name__ == "__main__":
    main()
