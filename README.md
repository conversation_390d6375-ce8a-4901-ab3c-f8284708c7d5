# Qwen2.5文本审核模型微调项目

基于Qwen2.5-7B的文本内容审核模型微调方案，使用LoRA技术在单卡A800环境下进行高效微调。

## 项目概述

本项目实现了一个完整的文本审核业务模型微调流程，支持对文本内容进行三分类审核：
- **合规**: 内容健康正面，符合平台规范
- **违规**: 内容明显违反平台规范，包含有害信息
- **疑似**: 内容存在争议或需要进一步人工审核

## 技术特点

- 🚀 基于Qwen2.5-7B-Instruct模型
- 🔧 使用LoRA高效微调技术
- 💾 优化单卡A800显存使用
- 📊 完整的训练监控和评估
- 🎯 专门针对文本审核业务优化
- 📝 详细的中文注释和文档

## 环境要求

### 硬件要求
- GPU: NVIDIA A800 (80GB显存) 或同等性能GPU
- 内存: 建议32GB以上
- 存储: 建议100GB以上可用空间

### 软件要求
- 操作系统: Ubuntu 18.04+
- Python: 3.8+
- CUDA: 11.8+
- PyTorch: 2.1.0+

## 快速开始

### 1. 环境安装

```bash
# 克隆项目
git clone <your-repo-url>
cd fine_tuning2

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 准备数据

项目支持自动生成示例数据，也可以使用自定义数据：

#### 使用示例数据
```bash
python train.py --create_sample_data --sample_data_size 1000
```

#### 使用自定义数据
数据格式为JSONL，每行包含：
```json
{
  "text": "待审核的文本内容",
  "label": "合规",
  "label_id": 0
}
```

将数据文件放置在以下位置：
- `./data/train.jsonl` - 训练数据
- `./data/validation.jsonl` - 验证数据  
- `./data/test.jsonl` - 测试数据

### 3. 开始训练

#### 基础训练
```bash
python train.py
```

#### 自定义参数训练
```bash
python train.py \
  --output_dir ./my_output \
  --num_epochs 5 \
  --batch_size 4 \
  --learning_rate 1e-4 \
  --lora_r 32 \
  --max_seq_length 1024
```

#### 配置文件训练
```bash
# 首先生成配置文件
python -c "from config import Config; config = Config(); config.save_config('my_config.json')"

# 使用配置文件训练
python train.py --config my_config.json
```

### 4. 模型推理

#### 单条文本审核
```bash
python inference.py \
  --base_model Qwen/Qwen2.5-7B-Instruct \
  --lora_weights ./output/lora_weights \
  --mode single \
  --text "这是一条需要审核的文本内容"
```

#### 交互式审核
```bash
python inference.py \
  --base_model Qwen/Qwen2.5-7B-Instruct \
  --lora_weights ./output/lora_weights \
  --mode single
```

#### 批量审核
```bash
python inference.py \
  --base_model Qwen/Qwen2.5-7B-Instruct \
  --lora_weights ./output/lora_weights \
  --mode batch \
  --input_file ./data/test.jsonl \
  --output_file ./predictions.json
```

#### 模型评估
```bash
python inference.py \
  --base_model Qwen/Qwen2.5-7B-Instruct \
  --lora_weights ./output/lora_weights \
  --mode evaluate \
  --test_file ./data/test.jsonl \
  --eval_output ./evaluation_result.json
```

## 项目结构

```
fine_tuning2/
├── README.md                 # 项目说明文档
├── requirements.txt          # Python依赖包
├── config.py                # 配置管理模块
├── data_processor.py        # 数据处理模块
├── model_trainer.py         # 模型训练器
├── train.py                 # 训练主脚本
├── inference.py             # 推理脚本
├── utils.py                 # 工具函数
├── data/                    # 数据目录
│   ├── train.jsonl         # 训练数据
│   ├── validation.jsonl    # 验证数据
│   └── test.jsonl          # 测试数据
├── output/                  # 输出目录
│   ├── lora_weights/       # LoRA权重
│   ├── tokenizer/          # 分词器
│   ├── training_config.json # 训练配置
│   ├── model_info.json     # 模型信息
│   └── training_logs.json  # 训练日志
└── cache/                   # 缓存目录
```

## 配置说明

### 核心配置参数

#### LoRA配置
- `r`: LoRA秩，控制参数量和性能平衡 (推荐: 32-64)
- `lora_alpha`: LoRA缩放参数 (推荐: r的2倍)
- `lora_dropout`: LoRA dropout率 (推荐: 0.1)

#### 训练配置
- `num_train_epochs`: 训练轮数 (推荐: 3-5)
- `per_device_train_batch_size`: 单卡批次大小 (A800推荐: 2-4)
- `gradient_accumulation_steps`: 梯度累积步数 (推荐: 8-16)
- `learning_rate`: 学习率 (推荐: 1e-4 到 5e-4)

#### 数据配置
- `max_seq_length`: 最大序列长度 (推荐: 1024-2048)
- `preprocessing_num_workers`: 预处理工作进程数

### 内存优化配置

针对A800单卡环境的优化设置：
- 使用bfloat16混合精度训练
- 启用Flash Attention 2
- 梯度检查点
- 动态padding
- 合理的批次大小和梯度累积

## 性能优化

### 显存优化
1. **混合精度训练**: 使用bf16减少显存占用
2. **梯度累积**: 通过累积实现大批次效果
3. **动态padding**: 避免不必要的padding
4. **Flash Attention**: 提升注意力计算效率

### 训练加速
1. **数据预处理**: 多进程并行处理
2. **模型编译**: 使用torch.compile (PyTorch 2.0+)
3. **缓存机制**: 缓存预处理结果
4. **按长度分组**: 减少padding开销

## 监控和日志

### 训练监控
- TensorBoard日志
- 实时损失曲线
- GPU内存使用监控
- 训练速度统计

### 评估指标
- 准确率 (Accuracy)
- 精确率 (Precision)
- 召回率 (Recall)
- F1分数
- 混淆矩阵

## 常见问题

### Q: 显存不足怎么办？
A: 可以尝试以下方法：
- 减小batch_size
- 增大gradient_accumulation_steps
- 减小max_seq_length
- 使用更小的LoRA秩

### Q: 训练速度慢怎么办？
A: 可以尝试：
- 增加dataloader_num_workers
- 使用group_by_length
- 启用Flash Attention
- 检查数据预处理效率

### Q: 如何调整模型性能？
A: 建议：
- 增加训练数据量
- 调整LoRA参数 (r, alpha)
- 优化学习率和训练轮数
- 使用数据增强技术

### Q: 如何部署模型？
A: 可以：
- 使用inference.py进行推理
- 集成到Web服务中
- 使用vLLM等推理框架
- 转换为ONNX格式

## 进阶使用

### 自定义数据格式
```python
# 在data_processor.py中修改数据加载逻辑
def load_custom_data(self, file_path):
    # 实现自定义数据加载
    pass
```

### 自定义评估指标
```python
# 在model_trainer.py中添加自定义指标
def compute_metrics(eval_pred):
    # 实现自定义评估指标
    pass
```

### 多GPU训练
```bash
# 使用torchrun进行多GPU训练
torchrun --nproc_per_node=2 train.py --batch_size 1
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 请确保在使用前仔细阅读Qwen2.5模型的使用条款和限制。
